plugins {
    id "org.jetbrains.kotlin.jvm" version "1.6.21"
    id "org.jetbrains.kotlin.plugin.spring" version "1.6.21"
    id "org.jetbrains.kotlin.plugin.jpa" version "1.6.21"
    id "org.jetbrains.kotlin.kapt" version "1.6.21"
    id "org.springframework.boot" version "2.4.5"
    id "io.spring.dependency-management" version "1.0.11.RELEASE"
    id "java"
    id "jacoco"
    id "net.linguica.maven-settings" version "0.5"
}

ext {
    set("springCloudVersion", "2020.0.2")
    set("springCloudStreamVersion", "3.1.1")
    set("feignVersion", "10.10.1")
    set("mysqlVersion", "8.0.16")
    set("h2Version", "1.4.199")
    set("hibernateVersion", "5.4.32.Final")
}

group = "com.pharmeasy.mercury"
version = ""

repositories {
    maven {
        name "nexus"
        url "https://nexus.mercuryonline.co/nexus/content/groups/public"
    }
    mavenCentral()
}

dependencies {
    // kotlin
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    // spring
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.2.6.RELEASE")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("org.springframework.cloud:spring-cloud-starter-sleuth")
    implementation("org.springframework.cloud:spring-cloud-starter-stream-kafka")
    implementation("org.springframework.retry:spring-retry:1.3.4")
    implementation("com.pharmeasy:stream-util:0.2.0")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-integration")
    implementation("org.springframework.integration:spring-integration-redis")
    implementation("com.pharmeasy.transactional-outbox:jpa:0.1.58")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor:1.6.4")


    // util
    implementation("com.pharmeasy:mercury-model:0.1.0")
    implementation("com.pharmeasy:micro-service-util:0.1.4")
    implementation("com.pharmeasy:header-context-util:0.1.0")
    implementation("com.pharmeasy:data-model:1.9.7")
    implementation("com.pharmeasy:compression-util:2024.0.0")
    implementation("com.pharmeasy:s3-util:2022.0.20")
    implementation("software.amazon.awssdk:s3")
    implementation("software.amazon.awssdk:sts")
    implementation("com.pharmeasy:notification-util:1.0.0")
    // 3rd party
    implementation("org.apache.kafka:kafka-streams")
    implementation("org.apache.commons:commons-csv:1.5")
    implementation("io.github.openfeign:feign-httpclient:$feignVersion")
    implementation("mysql:mysql-connector-java:$mysqlVersion")
    implementation("org.apache.velocity:velocity:1.7")
    implementation("org.apache.poi:poi:4.1.2")
    implementation('io.sentry:sentry-spring-boot-starter:4.3.0')
    implementation('io.sentry:sentry-logback:4.3.0')
    implementation("org.springframework.boot:spring-boot-starter-thymeleaf")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.statemachine:spring-statemachine-core:3.2.0")
    implementation 'org.mapstruct:mapstruct:1.4.2.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.2.Final'
    kapt("org.mapstruct:mapstruct-processor:1.4.2.Final")
    implementation("org.hibernate:hibernate-envers:5.4.30.FINAL")

    // test
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude(group : "org.junit.vintage", module : "junit-vintage-engine")
        implementation 'org.springdoc:springdoc-openapi-ui:1.8.0'
    }
    testImplementation("org.junit.jupiter:junit-jupiter:5.10.0")

    testImplementation("io.mockk:mockk:1.13.7")
    testImplementation 'org.mockito:mockito-junit-jupiter:4.0.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:4.0.0'

    testImplementation("com.h2database:h2:$h2Version")
    implementation("org.hibernate:hibernate-core:${hibernateVersion}")
    implementation("org.hibernate:hibernate-envers:${hibernateVersion}")

    implementation("com.pharmeasy:mercury-model:2024.0.2")
    testImplementation "org.jetbrains.kotlin:kotlin-test:1.6.21"


}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom("software.amazon.awssdk:bom:2.16.48")
    }
}

java.targetCompatibility = JavaVersion.VERSION_11
java.sourceCompatibility = JavaVersion.VERSION_11

compileKotlin {
	kotlinOptions {
		freeCompilerArgs = ["-Xjsr305=strict"]
		jvmTarget = "11"
	}
}

compileTestKotlin {
	kotlinOptions {
		freeCompilerArgs = ["-Xjsr305=strict"]
		jvmTarget = "11"
	}
}

kapt {
    includeCompileClasspath = true
    arguments {
        arg("mapstruct.verbose", "true")
    }
}

sourceSets {
    main.java.srcDirs += "src/main/kotlin"
    test.java.srcDirs += "src/test/kotlin"
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

jacoco {
    toolVersion = "0.8.7"
    reportsDir = file("$buildDir/reports/jacoco")
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.enabled = true
        html.enabled = true
    }

    afterEvaluate {
        classDirectories.from(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: ["**/model/**", "**/data/**"])
        }))
    }
}

check.dependsOn jacocoTestReport

allOpen {
    annotation("javax.persistence.Entity")
    annotation("javax.persistence.MappedSuperclass")
    annotation("javax.persistence.Embeddable")
}

test {
    useJUnitPlatform()
    finalizedBy jacocoTestReport
    doLast {
        println "Reports generated at ${buildDir.absolutePath}/reports/jacoco/test/html"
    }
}
