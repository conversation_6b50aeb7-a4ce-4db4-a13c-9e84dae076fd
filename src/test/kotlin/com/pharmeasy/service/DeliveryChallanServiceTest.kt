package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.model.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.proxy.WarehouseProxy
import com.pharmeasy.repo.DeliveryChallanLogEntryRepo
import com.pharmeasy.repo.DeliveryChallanMappingRepo
import com.pharmeasy.repo.DeliveryChallanRepo
import com.pharmeasy.repo.DeliveryChallanTaxLogRepo
import com.pharmeasy.repo.read.*
import com.pharmeasy.stream.DcCallBackEventPusher
import com.pharmeasy.type.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.kotlin.*
import org.mockito.quality.Strictness
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DeliveryChallanServiceTest {

    @Mock private lateinit var warehouseProxy: WarehouseProxy
    @Mock private lateinit var deliveryChallanRepo: DeliveryChallanRepo
    @Mock private lateinit var deliveryChallanReadRepo: DeliveryChallanReadRepo
    @Mock private lateinit var supplierProxy: SupplierProxy
    @Mock private lateinit var fileUploadService: FileUploadService
    @Mock private lateinit var companyService: CompanyService
    @Mock private lateinit var deliveryChallanMappingReadRepo: DeliveryChallanMappingReadRepo
    @Mock private lateinit var deliveryChallanLogEntryReadRepo: DeliveryChallanLogEntryReadRepo
    @Mock private lateinit var deliveryChallanTaxLogReadRepo: DeliveryChallanTaxLogReadRepo
    @Mock private lateinit var deliveryChallanLogEntryRepo: DeliveryChallanLogEntryRepo
    @Mock private lateinit var deliveryChallanMappingRepo: DeliveryChallanMappingRepo
    @Mock private lateinit var deliveryChallanTaxLogRepo: DeliveryChallanTaxLogRepo
    @Mock private lateinit var partnerService: PartnerService
    @Mock private lateinit var dcCallBackEventPusher: DcCallBackEventPusher

    private lateinit var deliveryChallanService: DeliveryChallanService

    // Sample test data
    private lateinit var sampleDeliveryChallan: DeliveryChallan
    private lateinit var sampleDeliveryChallanDto: DeliveryChallanDto
    private lateinit var sampleDeliveryChallanLogEntry: DeliveryChallanLogEntry
    private lateinit var sampleDeliveryChallanMapping: DeliveryChallanMapping
    private lateinit var sampleDeliveryChallanTaxLog: DeliveryChallanTaxLog
    private lateinit var sampleSetOffData: SetoffTaxData

    @BeforeEach
    fun setUp() {
        deliveryChallanService = spy(DeliveryChallanService(warehouseProxy))

        // Manually inject dependencies
        val serviceClass = DeliveryChallanService::class.java
        serviceClass.getDeclaredField("deliveryChallanRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanRepo) }
        serviceClass.getDeclaredField("deliveryChallanReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanReadRepo) }
        serviceClass.getDeclaredField("supplierProxy").apply { isAccessible = true; set(deliveryChallanService, supplierProxy) }
        serviceClass.getDeclaredField("fileUploadService").apply { isAccessible = true; set(deliveryChallanService, fileUploadService) }
        serviceClass.getDeclaredField("companyService").apply { isAccessible = true; set(deliveryChallanService, companyService) }
        serviceClass.getDeclaredField("deliveryChallanMappingReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanMappingReadRepo) }
        serviceClass.getDeclaredField("deliveryChallanLogEntryReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanLogEntryReadRepo) }
        serviceClass.getDeclaredField("deliveryChallanTaxLogReadRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanTaxLogReadRepo) }
        serviceClass.getDeclaredField("deliveryChallanLogEntryRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanLogEntryRepo) }
        serviceClass.getDeclaredField("deliveryChallanMappingRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanMappingRepo) }
        serviceClass.getDeclaredField("deliveryChallanTaxLogRepo").apply { isAccessible = true; set(deliveryChallanService, deliveryChallanTaxLogRepo) }
        serviceClass.getDeclaredField("partnerService").apply { isAccessible = true; set(deliveryChallanService, partnerService) }
        serviceClass.getDeclaredField("dcCallBackEventPusher").apply { isAccessible = true; set(deliveryChallanService, dcCallBackEventPusher) }

        // Initialize test data
        sampleDeliveryChallan = DeliveryChallan(
            id = 1L,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "SYSTEM",
            updatedBy = null,
            dcNumber = "DC123",
            amount = 1000.0,
            pendingAmount = 1000.0,
            status = DeliveryChallanStatusType.OPEN,
            tenant = "test_tenant",
            partnerDetailId = 123L,
            partnerId = 456L,
            partnerName = "Test Vendor",
            documentDate = LocalDate.now()
        )

        sampleDeliveryChallanDto = DeliveryChallanDto(
            tenant = "test_tenant",
            dcNumber = "DC123",
            dcValue = BigDecimal("1000.00"),
            pdi = 123L,
            items = mutableListOf(
                ItemDto(
                    debitNoteSettlementType = DcSettlementType.AUTO,
                    itemType = DcItemType.SALEABLE,
                    itemValue = 1000.0,
                    gst = 180.0
                )
            ),
            documentDate = LocalDate.now()
        )

        sampleDeliveryChallanLogEntry = DeliveryChallanLogEntry(
            id = 1L,
            createdOn = LocalDateTime.now(),
            createdBy = "SYSTEM",
            amount = 1000.0,
            referenceNumber = "REF123",
            referenceDate = LocalDate.now(),
            referenceAmount = 1000.0,
            referenceNumber2 = null,
            referenceAmount2 = null,
            status = DeliveryChallanLogEntryStatusType.CN_SETOFF,
            remark = "Test remark"
        )

        sampleDeliveryChallanMapping = DeliveryChallanMapping(
            id = 1L,
            createdOn = LocalDateTime.now(),
            deliveryChallanId = 1L,
            deliveryChallanLogId = 1L,
            dcAmountUsed = 1000.0,
            status = DeliveryChallanStatusType.OPEN
        )

        sampleDeliveryChallanTaxLog = DeliveryChallanTaxLog(
            id = 1L,
            deliveryChallanLogId = 1L,
            createdOn = LocalDateTime.now(),
            tax = 18.0,
            taxValue = 180.0,
            taxableValue = 1000.0,
            totalValue = 1180.0,
            hsn = "HSN123"
        )

        sampleSetOffData = SetoffTaxData(
            taxLevel = 18.0,
            taxableAmount = 1000.0,
            taxAmount = 180.0,
            grossAmount = 1180.0
        )
    }

    @Test
    fun `saveDeliveryChallan should not save when delivery challan already exists`() {
        // Given
        whenever(deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(
            sampleDeliveryChallanDto.dcNumber,
            sampleDeliveryChallanDto.tenant,
            sampleDeliveryChallanDto.pdi
        )).thenReturn(listOf(sampleDeliveryChallan))

        // When
        deliveryChallanService.saveDeliveryChallan(sampleDeliveryChallanDto)

        // Then
        verify(deliveryChallanRepo, never()).save(any())
    }

    @Test
    fun `getDeliveryChallanBySupplierId should return paginated results`() {
        // Given
        val deliveryChallanDataDto = DeliveryChallanDataDto(
            id = 1L,
            dcNumber = "DC123",
            createdOn = LocalDateTime.now(),
            amount = 1000.0,
            paidAmount = 0.0,
            status = DeliveryChallanStatusType.OPEN,
            documentDate = LocalDate.now()
        )
        val page = PageImpl(listOf(deliveryChallanDataDto), PageRequest.of(0, 10), 1)
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf("test_tenant"))
        whenever(deliveryChallanReadRepo.getSupplierDeliveryChallanData(
            any(), any(), any(), any(), any(), any(), any(), any(), any()
        )).thenReturn(page)

        // When
        val result = deliveryChallanService.getDeliveryChallanBySupplierId(
            123L, "test_tenant", 0, 10, "DC123", LocalDate.now(), LocalDate.now(), 456L, "Test Vendor", DeliveryChallanStatusType.OPEN
        )

        // Then
        assertEquals(1, result.elements)
        assertEquals(1, (result.data as List<*>).size)
    }


    @Test
    fun `getDeliveryChallanHistory should return history data`() {
        // Given
        whenever(deliveryChallanReadRepo.getOne(1L)).thenReturn(sampleDeliveryChallan)
        whenever(deliveryChallanMappingReadRepo.getByDeliveryChallanId(1L)).thenReturn(listOf(sampleDeliveryChallanMapping))
        whenever(deliveryChallanLogEntryReadRepo.getOne(1L)).thenReturn(sampleDeliveryChallanLogEntry)
        whenever(deliveryChallanTaxLogReadRepo.getByDeliveryChallanLogId(1L)).thenReturn(mutableListOf(sampleSetOffData))
        whenever(fileUploadService.getDownloadUrl(any(), any())).thenReturn(FileMetaUrlDto(1, mutableListOf()))

        // When
        val result = deliveryChallanService.getDeliveryChallanHistory(1L)

        // Then
        assertEquals("Test Vendor", result.partnerName)
        assertEquals(1000.0, result.dcAmount)
        assertEquals(0.0, result.setoffAmount)
        assertEquals(1, result.data.size)
    }

    @Test
    fun `getDeliveryChallanHistoryByDc should return history data`() {
        // Given
        whenever(deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(
            "DC123", "test_tenant", 123L
        )).thenReturn(listOf(sampleDeliveryChallan))
        whenever(deliveryChallanMappingReadRepo.getByDeliveryChallanId(1L)).thenReturn(listOf(sampleDeliveryChallanMapping))
        whenever(deliveryChallanLogEntryReadRepo.getOne(1L)).thenReturn(sampleDeliveryChallanLogEntry)
        whenever(deliveryChallanTaxLogReadRepo.getByDeliveryChallanLogId(1L)).thenReturn(mutableListOf(sampleSetOffData))
        whenever(fileUploadService.getDownloadUrl(any(), any())).thenReturn(FileMetaUrlDto(1, mutableListOf()))
        whenever(warehouseProxy.getUserByUserIds(any())).thenReturn(listOf(User("SYSTEM", "System User", "SYSTEM", "")))
        whenever(deliveryChallanReadRepo.getOne(1L)).thenReturn(sampleDeliveryChallan)

        // When
        val result = deliveryChallanService.getDeliveryChallanHistoryByDc("DC123", "test_tenant", 123L)

        // Then
        assertEquals("Test Vendor", result.partnerName)
        assertEquals(1000.0, result.dcAmount)
        assertEquals(0.0, result.setoffAmount)
        assertEquals(1, result.data.size)
    }

    @Test
    fun `getDocumentsByDc should return document URLs`() {
        // Given
        whenever(deliveryChallanReadRepo.getByDcNumberAndTenantAndPartnerDetailId(
            "DC123", "test_tenant", 123L
        )).thenReturn(listOf(sampleDeliveryChallan))
        whenever(deliveryChallanMappingReadRepo.getByDeliveryChallanId(1L)).thenReturn(listOf(sampleDeliveryChallanMapping))
        whenever(fileUploadService.getDownloadUrl(any(), any())).thenReturn(FileMetaUrlDto(1L, mutableListOf()))

        // When
        val result = deliveryChallanService.getDocumentsByDc("DC123", "test_tenant", 123L)

        // Then
        assertEquals(1, result.size)
    }
} 