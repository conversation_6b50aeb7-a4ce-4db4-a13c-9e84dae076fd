package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.model.advancepayment.*
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.repo.advancepayment.AdvancePayLinksDataRepo
import com.pharmeasy.repo.advancepayment.AdvancePaymentRepo
import com.pharmeasy.type.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.jpa.domain.Specification
import org.springframework.dao.PessimisticLockingFailureException
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import org.junit.jupiter.api.assertThrows
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import org.springframework.data.domain.Sort

// Helper classes for testing
@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AdvancePaymentServiceTest {

    @Mock private lateinit var advancePaymentRepo: AdvancePaymentRepo
    @Mock private lateinit var supplierProxy: SupplierProxy
    @Mock private lateinit var adjustmentService: AdjustmentService
    @Mock private lateinit var documentMasterService: DocumentMasterService
    @Mock private lateinit var vendorFileService: VendorFileService
    @Mock private lateinit var s3FileUtilityService: S3FileUtilityService
    @Mock private lateinit var advancePayLinksDataRepo: AdvancePayLinksDataRepo
    @Mock private lateinit var checkerService: CheckerService
    @Mock private lateinit var companyService: CompanyService
    @Mock private lateinit var chequeHandleService: ChequeHandlingService
    @Mock private lateinit var receiptService: ReceiptService
    @Mock private lateinit var partnerService: PartnerService
    @Mock private lateinit var vaultCheckerService: CheckerService

    private lateinit var advancePaymentService: AdvancePaymentService

    // Sample test data
    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping
    private lateinit var sampleAdvancePayment: AdvancePayment
    private lateinit var sampleAdvancePaymentDto: AdvancePaymentDto
    private lateinit var sampleUpdateAdvancePaymentDto: UpdateAdvancePaymentDto
    private lateinit var sampleCheckerDetails: CheckerDetails
    private lateinit var sampleCompany: Company
    private lateinit var sampleReceipt: Receipt

    @BeforeEach
    fun setUp() {
        // Create service instance with constructor parameter
        advancePaymentService = AdvancePaymentService("http://test-url")
        advancePaymentService = spy(
            AdvancePaymentService(
                url = "TODO()"
            )
        )
        // Manually inject dependencies
        val serviceClass = AdvancePaymentService::class.java
        serviceClass.getDeclaredField("advancePaymentRepo").apply { isAccessible = true; set(advancePaymentService, advancePaymentRepo) }
        serviceClass.getDeclaredField("supplierProxy").apply { isAccessible = true; set(advancePaymentService, supplierProxy) }
        serviceClass.getDeclaredField("adjustmentService").apply { isAccessible = true; set(advancePaymentService, adjustmentService) }
        serviceClass.getDeclaredField("documentMasterService").apply { isAccessible = true; set(advancePaymentService, documentMasterService) }
        serviceClass.getDeclaredField("vendorFileService").apply { isAccessible = true; set(advancePaymentService, vendorFileService) }
        serviceClass.getDeclaredField("s3FileUtilityService").apply { isAccessible = true; set(advancePaymentService, s3FileUtilityService) }
        serviceClass.getDeclaredField("advancePayLinksDataRepo").apply { isAccessible = true; set(advancePaymentService, advancePayLinksDataRepo) }
        serviceClass.getDeclaredField("checkerService").apply { isAccessible = true; set(advancePaymentService, checkerService) }
        serviceClass.getDeclaredField("companyService").apply { isAccessible = true; set(advancePaymentService, companyService) }
        serviceClass.getDeclaredField("chequeHandleService").apply { isAccessible = true; set(advancePaymentService, chequeHandleService) }
        serviceClass.getDeclaredField("receiptService").apply { isAccessible = true; set(advancePaymentService, receiptService) }
        serviceClass.getDeclaredField("partnerService").apply { isAccessible = true; set(advancePaymentService, partnerService) }
        serviceClass.getDeclaredField("vaultCheckerService").apply { isAccessible = true; set(advancePaymentService, vaultCheckerService) }

        // Initialize test data
        sampleCompanyTenantMapping = CompanyTenantMapping(1L, 1L, "test_tenant", "Test Tenant", 123L, 456L)
        
        sampleCheckerDetails = CheckerDetails(
            id = 1L, companyId = 1L, userName = "checker_user", userId = "checker123",
            type = CheckerType.CHECKER, email = "<EMAIL>", role = Role.APPROVER, isActive = true, priority = 0
        )
        
        sampleCompany = Company(
            id = 1L, companyCode = "TEST", name = "Test Company", darkStore = false, updatedBy = "user",
            cashBalance = BigDecimal.ZERO, bankBalance = BigDecimal.ZERO, enableRioAutoCn = false,
            isActive = true, rioEnabled = 1, isAutomail = false, bankSlipTemplateName = "template",
            dummyAccountPdi = 1L, isRetailerDnEnabled = false, isCnPrintEnabled = false, isFullCnSettlementEnabled = false
        )

        sampleAdvancePayment = AdvancePayment(
            id = 1L, createdOn = LocalDateTime.now(), updatedOn = LocalDateTime.now(),
            createdBy = "user123", createdByName = "Test User", updatedBy = "user123",
            userEmail = "<EMAIL>", assignedTo = "checker123", assignedToId = "checker123",
            approvalDate = LocalDate.now(), vendorName = "Test Vendor", documentId = "DOC123",
            typeOfAdvance = AdvanceType.INVOICE, type = PartnerType.VENDOR, status = Status.PENDING_APPROVAL,
            amount = BigDecimal("1000.00"), amountPending = BigDecimal("1000.00"), partnerId = 123L,
            partnerDetailId = 456L, tenant = "test_tenant", companyId = 1L, client = InvoiceType.VENDOR,
            remarks = "Test remarks", refDocuments = mutableListOf(), source = AdvancePaymentSource.SYSTEM
        )

        sampleAdvancePaymentDto = AdvancePaymentDto(
            amount = BigDecimal("1000.00"), partnerId = 123L, partnerName = "Test Vendor",
            partnerDetailId = 456L, tenant = "test_tenant", type = PartnerType.VENDOR,
            client = InvoiceType.VENDOR, typeOfAdvance = AdvanceType.INVOICE, remarks = "Test remarks",
            createdByName = "Test User", userEmail = "<EMAIL>",
            refDocuments = listOf(PaymentRefItem("REF123", LocalDate.now())), source = AdvancePaymentSource.SYSTEM
        )

        sampleUpdateAdvancePaymentDto = UpdateAdvancePaymentDto(
            amount = BigDecimal("1000.00"), paymentReference = "PAY123", paymentType = PaymentType.CASH,
            paymentDate = LocalDate.now(), bankName = "Test Bank", remarks = "Test update",
            tenant = "test_tenant", change = true, isRioTransaction = false
        )

        sampleReceipt = Receipt(
            id = 1L, receiptNumber = "REC123", createdAt = LocalDateTime.now(), createdBy = "user123",
            updatedAt = LocalDateTime.now(), updatedBy = "user123", paymentTransactionId = "TXN123",
            remarks = "Test receipt", amount = 1000.0, status = ReceiptStatus.GENERATED, advanceAmount = 500.0,
            source = AdvancePaymentSource.SYSTEM, advanceId = 1L
        )
    }

    @Test
    fun `createAdvancePayment should create advance payment successfully`() {
        // Given
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(sampleCompanyTenantMapping)
        whenever(checkerService.findChecker("test_tenant")).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.save(any<AdvancePayment>())).thenReturn(sampleAdvancePayment)

        // When
        val result = advancePaymentService.createAdvancePayment(sampleAdvancePaymentDto, "user123")

        // Then
        assertNotNull(result)
        assertEquals(1L, result.id)
        verify(advancePaymentRepo).save(any<AdvancePayment>())
    }

    @Test
    fun `createAdvancePayment should throw exception when no tenant mapping found`() {
        // Given
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(null)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.createAdvancePayment(sampleAdvancePaymentDto, "user123")
        }
        assertTrue(exception.message!!.contains("no tenant mapping found"))
    }

    @Test
    fun `createAdvancePayment should throw exception when no checker found`() {
        // Given
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(sampleCompanyTenantMapping)
        whenever(checkerService.findChecker("test_tenant")).thenReturn(mutableListOf())

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.createAdvancePayment(sampleAdvancePaymentDto, "user123")
        }
        assertTrue(exception.message!!.contains("No checker found"))
    }

    @Test
    fun `getAllAdvancePayment should return paginated results`() {
        // Given
        val page = PageImpl(listOf(sampleAdvancePayment), PageRequest.of(0, 10), 1)
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(sampleCompanyTenantMapping)
        whenever(advancePaymentRepo.findAll(any<Specification<AdvancePayment>>(), any<PageRequest>())).thenReturn(page)

        // When
        val result = advancePaymentService.getAllAdvancePayment(
            0, 10, null, null, null, null, null, null, null,
            null, null, null, null, "test_tenant", null, null
        )

        // Then
        assertEquals(1, result.elements)
        assertEquals(1, (result.data as List<*>).size)
    }

    @Test
    fun `getAllAdvancePayment should throw exception when no tenant mapping found`() {
        // Given
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(null)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.getAllAdvancePayment(
                0, 10, null, null, null, null, null, null, null,
                null, null, null, null, "test_tenant", null, null
            )
        }
        assertTrue(exception.message!!.contains("No tenant mapping found"))
    }

    @Test
    fun `getAllVendorAdvancePayment should return paginated results`() {
        // Given
        val pagination = PageRequest.of(0, 10)
        val vendorAdvancePayments = listOf(
            VendorAdvancePaymentDto(123L, 456L, "", 5, 2, 3, BigDecimal("1500.00"))
        )
        val page = PageImpl(vendorAdvancePayments, pagination, 1)
        val tenants: MutableList<String?> = mutableListOf("test_tenant")
        val partnerIds = listOf(123L)
        val clientTypes = listOf(InvoiceType.VENDOR)

        whenever(companyService.findTenants("test_tenant")).thenReturn(tenants)
        whenever(supplierProxy.supplier(listOf(123L))).thenReturn(listOf(Supplier(123L, "Test Vendor", null, mutableListOf(), null, null)))
        whenever(advancePaymentRepo.getVendorAdvancePaymentWithPartnerIds(tenants, partnerIds, clientTypes, PartnerType.CUSTOMER, pagination)).thenReturn(page)
        // When
        val result = advancePaymentService.getAllVendorAdvancePayment(
            0, 10, partnerIds, "test_tenant", null, clientTypes, null
        )


        // Then
        assertEquals(1, result.elements)
        val resultPage = result.data as Page<*>
        assertEquals(1, resultPage.content.size)
    }

    @Test
    fun `getAllVendorAdvancePayment should throw exception when no tenants found`() {
        // Given
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf())

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.getAllVendorAdvancePayment(
                0, 10, null, "test_tenant", null, listOf(InvoiceType.VENDOR), null
            )
        }
        assertTrue(exception.message!!.contains("is not mapped to any Company"))
    }

    @Test
    fun `getAggregatedAdvPayment should return aggregated data`() {
        // Given
        val aggregatedData = AggregatedAdvancePaymentDto(3, BigDecimal("1500.00"))
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf("test_tenant"))
        whenever(advancePaymentRepo.getAggregatedAdvPaymentWithPartnerIds(any(), any(), any())).thenReturn(aggregatedData)

        // When
        val result = advancePaymentService.getAggregatedAdvPayment(
            listOf(123L), "test_tenant", listOf(InvoiceType.VENDOR), null
        )

        // Then
        assertNotNull(result)
        assertEquals(3, result.totalPendingAdvancePayment)
        assertEquals(BigDecimal("1500.00"), result.totalPendingAdvancePayAmount)
    }

    @Test
    fun `getVendorAdvancePayment should return paginated results`() {
        // Given
        val partnerId = 123L
        val tenant = "test_tenant"
        val pagination = PageRequest.of(0, 10, Sort.Direction.DESC, "updatedOn")
        val advancePayments = listOf(sampleAdvancePayment)
        val page = PageImpl(advancePayments, pagination, 1)

        // Mocks
        whenever(companyService.findTenants(tenant)).thenReturn(mutableListOf(tenant))
        whenever(
            advancePaymentRepo.getAdvancePaySupplierTotal(
                partnerId,
                null, // documentId
                null, // refDocumentNum
                null, // transactionFrom
                null, // transactionTo
                null, // type
                mutableListOf(tenant), // tenants
                PartnerType.CUSTOMER,
                pagination
            )
        ).thenReturn(page)

        // When
        val result = advancePaymentService.getVendorAdvancePayment(
            page = 0,
            size = 10,
            partnerId = partnerId,
            status = null,
            documentId = null,
            refDocumentNum = null,
            transactionFrom = null,
            transactionTo = null,
            type = null,
            tenant = tenant
        )

        // Then
        assertEquals(1, result.elements)
        assertEquals(1, (result.data as List<*>).size)
    }

    @Test
    fun `checkerAdvancePayment should approve advance payment successfully`() {
        // Given
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.of(sampleAdvancePayment))
        whenever(checkerService.compareChecker(any(), eq("checker123"))).thenReturn(sampleCheckerDetails)
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf("test_tenant"))
        whenever(companyService.getCompanyByTenant("test_tenant")).thenReturn(sampleCompany)
        whenever(documentMasterService.getDocumentNumber(any(), any(), any())).thenReturn("DOC123")
        whenever(advancePaymentRepo.save(any<AdvancePayment>())).thenReturn(sampleAdvancePayment)

        // When
        val result = advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")

        // Then
        assertEquals(200, result.code)
        assertEquals("SUCCESS", result.message)
    }

    @Test
    fun `checkerAdvancePayment should throw exception when advance payment not found`() {
        // Given
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.empty())

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")
        }
        assertTrue(exception.message!!.contains("is not present in our DB"))
    }

    @Test
    fun `checkerAdvancePayment should throw exception when user is creator`() {
        // Given
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.of(sampleAdvancePayment))

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "user123")
        }
        assertTrue(exception.message!!.contains("Cannot approve the request as created by you"))
    }

    @Test
    fun `checkerAdvancePayment should return success when already approved`() {
        // Given
        val approvedPayment = sampleAdvancePayment.copy(status = Status.APPROVED)
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.of(approvedPayment))

        // When
        val result = advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")

        // Then
        assertEquals(200, result.code)
        assertEquals("SUCCESS", result.message)
    }

    @Test
    fun `checkerAdvancePayment should reject when change is false`() {
        // Given
        val rejectDto = sampleUpdateAdvancePaymentDto.copy(change = false)
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.of(sampleAdvancePayment))
        whenever(checkerService.compareChecker(any(), eq("checker123"))).thenReturn(sampleCheckerDetails)
        whenever(advancePaymentRepo.save(any<AdvancePayment>())).thenReturn(sampleAdvancePayment)

        // When
        val result = advancePaymentService.checkerAdvancePayment(1L, rejectDto, "checker123")

        // Then
        assertEquals(200, result.code)
        assertEquals("SUCCESS", result.message)
    }

    @Test
    fun `checkerAdvancePayment should throw exception on pessimistic locking failure`() {
        // Given
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.of(sampleAdvancePayment))
        whenever(checkerService.compareChecker(any(), eq("checker123"))).thenReturn(sampleCheckerDetails)
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf("test_tenant"))
        whenever(companyService.getCompanyByTenant("test_tenant")).thenReturn(sampleCompany)
        whenever(documentMasterService.getDocumentNumber(any(), any(), any())).thenReturn("DOC123")
        whenever(advancePaymentRepo.save(any<AdvancePayment>())).thenThrow(PessimisticLockingFailureException("Lock failure"))

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.checkerAdvancePayment(1L, sampleUpdateAdvancePaymentDto, "checker123")
        }
        assertTrue(exception.message!!.contains("The record was already processed"))
    }

    @Test
    fun `getAdvanceOutstanding should return outstanding amount`() {
        // Given
        whenever(advancePaymentRepo.getAdvancePaymentOutstanding("test_tenant", 456L)).thenReturn(BigDecimal("1500.00"))

        // When
        val result = advancePaymentService.getAdvanceOutstanding(456L, "test_tenant")

        // Then
        assertEquals(BigDecimal("1500.00"), result)
    }

    @Test
    fun `getAdvanceOutstanding should return zero when null`() {
        // Given
        whenever(advancePaymentRepo.getAdvancePaymentOutstanding("test_tenant", 456L)).thenReturn(null)

        // When
        val result = advancePaymentService.getAdvanceOutstanding(456L, "test_tenant")

        // Then
        assertEquals(BigDecimal.ZERO, result)
    }

    @Test
    fun `getAdvancePaymentById should return advance payment`() {
        // Given
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.of(sampleAdvancePayment))

        // When
        val result = advancePaymentService.getAdvancePaymentById(1L)

        // Then
        assertNotNull(result)
        assertEquals(1L, result.id)
    }

    @Test
    fun `getAdvancePaymentById should throw exception when not found`() {
        // Given
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.empty())

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.getAdvancePaymentById(1L)
        }
        assertTrue(exception.message!!.contains("Advance Payment not found for id"))
    }

    @Test
    fun `getAdvancePaymentByDocumentNumber should return advance payment`() {
        // Given
        whenever(advancePaymentRepo.getByDocumentId("DOC123")).thenReturn(sampleAdvancePayment)

        // When
        val result = advancePaymentService.getAdvancePaymentByDocumentNumber("DOC123")

        // Then
        assertNotNull(result)
        assertEquals("DOC123", result.documentId)
    }

    @Test
    fun `getAdvancePaymentByDocumentNumber should throw exception when not found`() {
        // Given
        whenever(advancePaymentRepo.getByDocumentId("INVALID")).thenReturn(null)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.getAdvancePaymentByDocumentNumber("INVALID")
        }
        assertTrue(exception.message!!.contains("Advance Payment not found for documentNumber"))
    }

    @Test
    fun `getAdvancePayUrl should create file and return success`() {
        // Given
        val vendorAdvancePaymentDto = VendorAdvancePaymentDto(123L, 456L, "", 5, 2, 3, BigDecimal("1500.00"))
        val vendorDataLinks = VendorDataLinks(1L, LocalDateTime.now(), LocalDateTime.now(), "IN_PROGRESS", VendorDataEnum.ADVANCE_PAYMENT, null, "SYSTEM", "th124", false)
        
        whenever(companyService.findTenants("th124")).thenReturn(mutableListOf("th124"))
        whenever(advancePaymentRepo.getAdvancePaymentDataForUrl(mutableListOf("th124"))).thenReturn(mutableListOf(vendorAdvancePaymentDto))
        whenever(supplierProxy.supplier(listOf(123L), null)).thenReturn(listOf(Supplier(123L, "Test Vendor", null, mutableListOf(), null, null)))
        whenever(vendorFileService.saveReport(eq("th124"), any())).thenReturn(vendorDataLinks)

        // When
        val result = advancePaymentService.getAdvancePayUrl("th124", "SYSTEM")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
    }

    @Test
    fun `getAdvancePayUrl should return no data when no tenants found`() {
        // Given
        whenever(companyService.findTenants("test_tenant")).thenReturn(mutableListOf())

        // When
        val result = advancePaymentService.getAdvancePayUrl("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("No Data", result.message)
    }

    @Test
    fun `advancePayDownload should return file link when available`() {
        // Given
        val vendorDataLink = VendorDataLinks(1L, LocalDateTime.now(), LocalDateTime.now(), "http://test-link.com", VendorDataEnum.ADVANCE_PAYMENT, null, "user123", "test_tenant", false)
        whenever(advancePayLinksDataRepo.getAdvancePayLink("user123", "test_tenant", false)).thenReturn(listOf(vendorDataLink))

        // When
        val result = advancePaymentService.advancePayDownload("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
        assertEquals("http://test-link.com", result.url)
    }

    @Test
    fun `advancePayDownload should return null when no file available`() {
        // Given
        whenever(advancePayLinksDataRepo.getAdvancePayLink("user123", "test_tenant", false)).thenReturn(emptyList())

        // When
        val result = advancePaymentService.advancePayDownload("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
        assertNull(result.url)
    }

    @Test
    fun `advancePayDetailDownload should return file link when available`() {
        // Given
        val vendorDataLink = VendorDataLinks(1L, LocalDateTime.now(), LocalDateTime.now(), "http://test-detail-link.com", VendorDataEnum.ADVANCE_PAY_DETAIL, null, "user123", "test_tenant", false)
        whenever(advancePayLinksDataRepo.getAdvancePayDetailLink("test_tenant", "user123", false)).thenReturn(listOf(vendorDataLink))

        // When
        val result = advancePaymentService.advancePayDetailDownload("test_tenant", "user123")

        // Then
        assertEquals(200, result.code)
        assertEquals("Success", result.message)
        assertEquals("http://test-detail-link.com", result.url)
    }

    @Test
    fun `updateAdvancePaymentChecker should update checker successfully`() {
        // Given
        val mockAdvancePayment = sampleAdvancePayment.copy(status = Status.PENDING_APPROVAL)
        whenever(checkerService.findCheckers("test_tenant", 1L, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.get(1L)).thenReturn(mockAdvancePayment)
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(sampleCompanyTenantMapping)
        whenever(vaultCheckerService.getCheckerByCompanyId(1L)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.save(any<AdvancePayment>())).thenAnswer { it.arguments[0] as AdvancePayment }

        // When
        val result = advancePaymentService.updateAdvancePaymentChecker(1L, 1L, "test_tenant", "user123")

        // Then
        assertEquals("success", result.message)
    }

    @Test
    fun `updateAdvancePaymentChecker should throw exception when no checker found`() {
        // Given
        whenever(checkerService.findCheckers("test_tenant", 1L, null)).thenReturn(mutableListOf())

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.updateAdvancePaymentChecker(1L, 1L, "test_tenant", "user123")
        }
        assertTrue(exception.message!!.contains("No active checker found"))
    }

    @Test
    fun `updateAdvancePaymentChecker should throw exception when advance payment not found`() {
        // Given
        whenever(checkerService.findCheckers("test_tenant", 1L, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(advancePaymentRepo.get(1L)).thenReturn(null)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.updateAdvancePaymentChecker(1L, 1L, "test_tenant", "user123")
        }
        assertTrue(exception.message!!.contains("No advance Pay entry found"))
    }

    @Test
    fun `cancelAdvancePayment should cancel advance payment successfully`() {
        // Given
        val userId = "checker123"
        val advanceId = 1L

        val mockAdvancePayment = sampleAdvancePayment.copy(
            id = advanceId,
            status = Status.APPROVED,
            consumed = false,
            amount = BigDecimal(1000),
            amountPending = BigDecimal(1000),
            tenant = "test_tenant"
        )

        whenever(advancePaymentRepo.get(advanceId)).thenReturn(mockAdvancePayment)
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(checkerService.compareChecker(any(), eq(userId))).thenReturn(sampleCheckerDetails)
        whenever(companyService.getCompanyCodeByTenant("test_tenant")).thenReturn("TEST")
        doNothing().whenever(advancePaymentService).createReversalLedgerEntry(mockAdvancePayment, userId, "TEST")
        whenever(advancePaymentRepo.save(any<AdvancePayment>())).thenAnswer { it.arguments[0] as AdvancePayment }

        // When
        val result = advancePaymentService.cancelAdvancePayment(userId, advanceId)

        // Then
        assertEquals("success", result.message)
        assertEquals(Status.CANCELLED, mockAdvancePayment.status)
        assertEquals(userId, mockAdvancePayment.updatedBy)
        assertNotNull(mockAdvancePayment.updatedOn)

        verify(advancePaymentRepo).get(advanceId)
        verify(checkerService).findCheckers("test_tenant", null, null)
        verify(checkerService).compareChecker(any(), eq(userId))
        verify(companyService).getCompanyCodeByTenant("test_tenant")
        verify(advancePaymentService).createReversalLedgerEntry(mockAdvancePayment, userId, "TEST")
        verify(advancePaymentRepo).save(mockAdvancePayment)
    }

    @Test
    fun `cancelAdvancePayment should throw exception when advance payment not found`() {
        // Given
        whenever(advancePaymentRepo.get(1L)).thenReturn(null)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.cancelAdvancePayment("user123", 1L)
        }
        assertTrue(exception.message!!.contains("not found"))
    }

    @Test
    fun `cancelAdvancePayment should throw exception when already consumed`() {
        // Given
        val consumedAdvancePayment = sampleAdvancePayment.copy(consumed = true)
        whenever(advancePaymentRepo.get(1L)).thenReturn(consumedAdvancePayment)
        whenever(checkerService.findCheckers("test_tenant", null, null)).thenReturn(mutableListOf(sampleCheckerDetails))
        whenever(checkerService.compareChecker(any(), eq("checker123"))).thenReturn(sampleCheckerDetails)

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.cancelAdvancePayment("checker123", 1L)
        }
        assertTrue(exception.message!!.contains("used in settlement"))
    }

    @Test
    fun `changeAdvancePaymentStatus should change status successfully`() {
        // Given
        val advancePaymentId = 1L
        val mockAdvancePayment = sampleAdvancePayment.copy(
            id = advancePaymentId,
            status = Status.PENDING_APPROVAL,
            tenant = "test_tenant",
            type = PartnerType.VENDOR,
            paymentType = PaymentType.CASH,
            amount = BigDecimal(1000),
            vendorName = "Test Vendor",
            documentId = "DOC123",
            partnerId = 123L,
            partnerDetailId = 456L,
            createdBy = "user1",
            assignedTo = "assignee1",
            assignedToId = "assigneeId1",
            client = InvoiceType.RIO
        )

        val sampleCompanyInstance = sampleCompany.copy(id = 999L)

        // 👇 Fix: Mock findById(), not findByIdOrNull()
        whenever(advancePaymentRepo.findById(advancePaymentId)).thenReturn(Optional.of(mockAdvancePayment))

        whenever(companyService.getCompanyByTenant("test_tenant")).thenReturn(sampleCompanyInstance)
        whenever(advancePaymentRepo.save(any<AdvancePayment>())).thenAnswer { it.arguments[0] as AdvancePayment }

        doNothing().whenever(adjustmentService).addAdjustmentToLedger(any(), any(), isNull())
        doNothing().whenever(adjustmentService).updateVendorBalance(any())

        // When
        val result = advancePaymentService.changeAdvancePaymentStatus(advancePaymentId, Status.APPROVED)

        // Then
        assertEquals(Status.APPROVED, result.status)
        verify(adjustmentService).addAdjustmentToLedger(any(), any(), isNull())
        verify(adjustmentService).updateVendorBalance(any())
        verify(advancePaymentRepo).save(mockAdvancePayment)
    }

    @Test
    fun `changeAdvancePaymentStatus should throw exception when advance payment not found`() {
        // Given
        whenever(advancePaymentRepo.findById(1L)).thenReturn(Optional.empty())

        // When & Then
        val exception = assertThrows(RequestException::class.java) {
            advancePaymentService.changeAdvancePaymentStatus(1L, Status.APPROVED)
        }
        assertTrue(exception.message!!.contains("is not present in our DB"))
    }

    @Test
    fun `checkAndUpdateLedgerForAdvancePayment should update ledger successfully`() {
        // Given
        whenever(advancePaymentRepo.getByDocumentId("DOC123")).thenReturn(sampleAdvancePayment)
        whenever(receiptService.getReceiptByAdvancePaymentId(1L)).thenReturn(sampleReceipt)
        whenever(partnerService.getExistingLedgerAmount(456L, PartnerType.VENDOR, "test_tenant", "DOC123", "REC123")).thenReturn(0.0)

        // When
        advancePaymentService.checkAndUpdateLedgerForAdvancePayment("user123", "DOC123")

        // Then
        verify(partnerService).addVendorLedgerEntry(eq("user123"), any<VendorLedgerDto>())
    }

    @Test
    fun `checkAndUpdateLedgerForAdvancePayment should throw exception when ledger already exists`() {
        // Given
        val referenceNumber = "DOC123"
        val user = "test_user"

        val advancePayment = sampleAdvancePayment.copy(amount = BigDecimal(1000))
        val receipt = sampleReceipt.copy(amount = 1100.0) // mismatch to make xor true

        whenever(advancePaymentRepo.getByDocumentId(referenceNumber)).thenReturn(advancePayment)
        whenever(receiptService.getReceiptByAdvancePaymentId(1L)).thenReturn(receipt)
        whenever(
            partnerService.getExistingLedgerAmount(
                456L, PartnerType.VENDOR, "test_tenant", "DOC123", "REC123"
            )
        ).thenReturn(1000.0)

        // When / Then
        val exception = assertThrows<RequestException> {
            advancePaymentService.checkAndUpdateLedgerForAdvancePayment(user, referenceNumber)
        }

        assertTrue(exception.message!!.contains("Ledger already exist for transaction $referenceNumber"))

        verify(partnerService, never()).addVendorLedgerEntry(any(), any())
    }
}