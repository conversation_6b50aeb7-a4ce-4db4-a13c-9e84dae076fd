package com.pharmeasy.service

import com.pharmeasy.data.BkBank
import com.pharmeasy.data.BkInvoice
import com.pharmeasy.exception.RequestException
import com.pharmeasy.data.CompanyTenantMapping
import com.pharmeasy.data.Settlement
import com.pharmeasy.data.SettlementTask
import com.pharmeasy.data.SettlementTaskItem
import com.pharmeasy.model.InvoiceStatus
import com.pharmeasy.model.ProcessSettlementTaskDto
import com.pharmeasy.repo.BkInvoiceRepo
import com.pharmeasy.repo.SettlementTaskCsvMappingRepo
import com.pharmeasy.repo.SettlementTaskItemRepo
import com.pharmeasy.repo.SettlementTaskRepo
import com.pharmeasy.repo.read.SettlementTaskItemReadRepo
import com.pharmeasy.type.APIVersionType
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SelectionType
import com.pharmeasy.type.SettlementFileStatus
import com.pharmeasy.type.SettlementTaskStatus
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers
import org.mockito.Mockito
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import java.time.LocalDate
import java.time.LocalDateTime.now
import java.util.Optional
import kotlin.test.assertEquals
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SettlementTaskProcessorTest {

    @Mock
    private lateinit var settlementTaskItemRepo: SettlementTaskItemRepo

    @Mock
    private lateinit var settlementTaskItemReadRepo: SettlementTaskItemReadRepo

    @Mock
    private lateinit var settlementService: SettlementService

    @Mock
    private lateinit var settlementTaskRepo: SettlementTaskRepo

    @Mock
    private lateinit var invoiceRepo: BkInvoiceRepo

    @Mock
    private lateinit var settlementTaskCsvMappingRepo: SettlementTaskCsvMappingRepo

    private lateinit var settlementTaskProcessor: SettlementTaskProcessor

    val company = CompanyTenantMapping(
        id = 1,
        companyId = 1,
        tenant = "th124",
        tenantName = "TEST",
        theaId = 1,
        partnerDetailId = 1
    )
    val bank = BkBank(
        id = 1,
        name = "TEST"
    )
    val parentTask = SettlementTask(
        id = 1L,
        settlementMode = PaymentType.CASH,
        taskStatus = SettlementTaskStatus.IN_PROGRESS,
        createdOn = now(),
        updatedOn = now(),
        createdBy = "SYSTEM",
        verifiedBy = null,
        verifiedOn = now(),
        fileStatus = SettlementFileStatus.VALIDATION_IN_PROGRESS,
        s3Uri = "",
        partnerType = PartnerType.CUSTOMER,
        company = company,
        isPartnerLevel = true
    )
    val taskItem = SettlementTaskItem(
        id = 1L,
        invoiceNumber = "INV001",
        amount = 1000.0,
        partnerDetailId = 1L,
        settlementTask = parentTask,
        createdOn = now(),
        updatedOn = now(),
        settled = false,
        chequeOrRefNo = "REF-123",
        bank = bank,
        paymentDate = LocalDate.now(),
        chequeDate = LocalDate.now()
    )

    val invoice = BkInvoice(
        invoiceNum = "INV001",
        amount = 1000.0,
        paidAmount = 0.0,
        status = InvoiceStatus.PENDING,
        id = 1,
        createdOn = now(),
        updatedOn = now(),
        createdBy = "SYSTEM",
        updatedBy = "SYSTEM",
        invoiceId = "INV001",
        supplierId = 1,
        supplierName = "TEST",
        dueDate = LocalDate.now(),
        items = mutableListOf(),
        settledOn = null,
        purchaseType = null,
        partnerId = 1,
        partnerDetailId = 1,
        tenant = "th124",
        type = PartnerType.CUSTOMER,
        client = InvoiceType.RIO,
        settlementId = null,
        settlementNumber = null,
        gatePassInvoice = null,
        tcsAmount = null,
        tcs = null,
        distributorPdi = 1,
        apiVersion = APIVersionType.V1,
        parentTenant = null,
        isMigrated = false
    )

    val settlement = Settlement(
        id = 1,
        createdOn = now(),
        updatedOn = now(),
        createdBy = "SYSTEM",
        supplierId = 1,
        supplierName = "TEST",
        amount = 1000.0,
        paidAmount = 1000.0,
        remarks = "",
        settlementNumber = "SET-1",
        invoices = mutableListOf(invoice),
        creditNotes = mutableListOf(),
        paymentType = PaymentType.CASH,
        paymentReference = "REF-001",
        paymentDate = LocalDate.now(),
        partnerId = 1,
        partnerDetailId = 1,
        type = PartnerType.CUSTOMER,
        tenant = "th124",
        chequeDate = LocalDate.now(),
        bankId = 1,
        bankName = "TEST",
        isBounced = false,
        reversed = false,
        advancePayment = mutableListOf(),
        chargeInvoice = mutableListOf(),
        charge = false,
        receipt = listOf(),
        paymentSource = AdvancePaymentSource.SYSTEM,
        retailerDebitNotes = mutableListOf(),
        uuid = "TEST-UUID-123"
    )

    @BeforeEach
    fun setUp() {
        settlementTaskProcessor = SettlementTaskProcessor(
            maxCashLimit = 200000.0, // 2 Lakh max cash limit
            settlementTaskItemRepo = settlementTaskItemRepo,
            settlementTaskItemReadRepo = settlementTaskItemReadRepo,
            settlementService = settlementService,
            settlementTaskRepo = settlementTaskRepo,
            invoiceRepo = invoiceRepo,
            settlementTaskCsvMappingRepo = settlementTaskCsvMappingRepo
        )

        // Common mock setups
        Mockito.`when`(settlementTaskRepo.findById(ArgumentMatchers.anyLong())).thenReturn(Optional.of(parentTask))
        Mockito.`when`(settlementTaskItemReadRepo.getPartnersBySettlementTask(ArgumentMatchers.anyLong())).thenReturn(listOf(1L))
        Mockito.`when`(settlementTaskItemReadRepo.getPartnersAmountBySettlementTask(ArgumentMatchers.anyLong(), ArgumentMatchers.anyLong())).thenReturn(1000.0)
        Mockito.`when`(settlementService.checkCashSettlementAmount(ArgumentMatchers.anyLong(), ArgumentMatchers.anyDouble())).thenReturn(true)
        Mockito.`when`(settlementTaskItemRepo.getAllItemsId(ArgumentMatchers.anyLong())).thenReturn(listOf(1L))
        Mockito.`when`(settlementTaskItemRepo.getAllItemsIdNotInList(ArgumentMatchers.anyLong(), ArgumentMatchers.anyList())).thenReturn(listOf(1L))
        Mockito.`when`(settlementTaskItemRepo.getOpenItems(1, listOf(1))).thenReturn(listOf(taskItem))
        Mockito.`when`(invoiceRepo.getByInvoiceNum(invoice.invoiceNum!!, mutableListOf(invoice.tenant))).thenReturn(listOf(invoice))
        Mockito.`when`(settlementService.save("SYSTEM", settlement, null)).thenReturn(settlement)
    }

    @Test
    fun `processTask should throw exception when task not found`() {
        // Given
        val taskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            settlementMode = PaymentType.CASH,
            selectionType = SelectionType.ALL,
            taskItemIdList = null
        )
        Mockito.`when`(settlementTaskRepo.findById(ArgumentMatchers.anyLong())).thenReturn(Optional.empty())

        // When/Then
        assertThrows<RequestException> {
            settlementTaskProcessor.processTask(taskDto, "user", null, "tenant", "userHash")
        }
    }

    @Test
    fun `processTask should throw exception when cash amount exceeds limit`() {
        // Given
        val taskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            settlementMode = PaymentType.CASH,
            selectionType = SelectionType.ALL,
            taskItemIdList = null
        )

        Mockito.`when`(settlementTaskItemReadRepo.getPartnersAmountBySettlementTask(ArgumentMatchers.anyLong(), ArgumentMatchers.anyLong())).thenReturn(250000.0)

        // When/Then
        assertThrows<RequestException> {
            settlementTaskProcessor.processTask(taskDto, "user", null, "tenant", "userHash")
        }
        assertEquals(SettlementTaskStatus.FAILED, parentTask.taskStatus)
    }

    @Test
    fun `processTask should process cash settlement successfully with ALL selection type`() {
        // Given
        val taskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            settlementMode = PaymentType.CASH,
            selectionType = SelectionType.ALL,
            taskItemIdList = null
        )

        // When
        settlementTaskProcessor.processTask(taskDto, "user", null, "tenant", "userHash")

        // Then
        Mockito.verify(settlementTaskRepo).save(ArgumentMatchers.any())
        assertEquals(SettlementTaskStatus.COMPLETE, parentTask.taskStatus)
    }

    @Test
    fun `processTask should process cheque settlement successfully with INCLUDE selection type`() {
        // Given
        val taskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            settlementMode = PaymentType.CHEQUE,
            selectionType = SelectionType.INCLUDE,
            taskItemIdList = listOf(1L, 2L)
        )

        // When
        settlementTaskProcessor.processTask(taskDto, "user", null, "tenant", "userHash")

        // Then
        Mockito.verify(settlementTaskRepo).save(ArgumentMatchers.any())
        assertEquals(SettlementTaskStatus.COMPLETE, parentTask.taskStatus)
    }

    @Test
    fun `processTask should process NEFT settlement successfully with EXCLUDE selection type`() {
        // Given
        val taskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            settlementMode = PaymentType.NEFT,
            selectionType = SelectionType.EXCLUDE,
            taskItemIdList = listOf(3L, 4L)
        )

        Mockito.`when`(settlementTaskItemRepo.getAllItemsIdNotInList(ArgumentMatchers.anyLong(), ArgumentMatchers.anyList())).thenReturn(listOf(1L))

        // When
        settlementTaskProcessor.processTask(taskDto, "user", null, "tenant", "userHash")

        // Then
        Mockito.verify(settlementTaskRepo).save(ArgumentMatchers.any())
        assertEquals(SettlementTaskStatus.COMPLETE, parentTask.taskStatus)
    }

    @Test
    fun `processTask should handle partial success`() {
        // Given
        val taskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            settlementMode = PaymentType.CASH,
            selectionType = SelectionType.ALL,
            taskItemIdList = null
        )

        // When
        settlementTaskProcessor.processTask(taskDto, "user", null, "tenant", "userHash")

        // Then
        Mockito.verify(settlementTaskRepo).save(ArgumentMatchers.any())
        assertEquals(SettlementTaskStatus.COMPLETE, parentTask.taskStatus)
    }
}