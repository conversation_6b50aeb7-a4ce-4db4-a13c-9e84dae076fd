package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.BkInvoiceReadRepo
import com.pharmeasy.type.*
import com.pharmeasy.util.SettlementTaskUtils
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import org.junit.jupiter.api.Assertions.*
import org.mockito.ArgumentMatchers.argThat
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.data.domain.PageImpl

@ExtendWith(MockitoExtension::class)
class SettlementFileValidatorTest {

    @Mock private lateinit var settlementTaskRepo: SettlementTaskRepo
    @Mock private lateinit var bankRepo: BkBankRepo
    @Mock private lateinit var s3Uploader: S3Uploader
    @Mock private lateinit var invoiceRepo: BkInvoiceReadRepo
    @Mock private lateinit var settlementTaskItemRepo: SettlementTaskItemRepo

    private lateinit var settlementFileValidator: SettlementFileValidator
    private lateinit var sampleSettlementTask: SettlementTask
    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping
    private lateinit var sampleBank: BkBank

    @BeforeEach
    fun setUp() {
        settlementFileValidator = SettlementFileValidator(
            settlementPrefix = "test-prefix",
            settlementTaskRepo = settlementTaskRepo,
            bankRepo = bankRepo,
            s3Uploader = s3Uploader,
            banks = mutableListOf(),
            invoiceRepo = invoiceRepo,
            settlementTaskItemRepo = settlementTaskItemRepo
        )

        sampleCompanyTenantMapping = CompanyTenantMapping(
            id = 1L,
            companyId = 456L,
            tenant = "test_tenant",
            tenantName = "test_tenant",
            theaId = 1,
            partnerDetailId = 123L
        )

        sampleBank = BkBank(
            id = 1L,
            name = "Test Bank"
        )

        sampleSettlementTask = SettlementTask(
            id = 1L,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user",
            settlementMode = PaymentType.CHEQUE,
            fileStatus = SettlementFileStatus.VALIDATION_IN_PROGRESS,
            taskStatus = SettlementTaskStatus.PENDING,
            s3Uri = "test-uri",
            partnerType = PartnerType.CUSTOMER,
            company = sampleCompanyTenantMapping,
            isPartnerLevel = true
        )

        // Setup common mocks
//        whenever(bankRepo.findAll()).thenReturn(listOf(sampleBank))
//        whenever(s3Uploader.read(any(), any())).thenReturn(ByteArrayInputStream("".toByteArray()))
    }

    @Test
    fun `validateFile should validate cheque file successfully`() {
        // Given
        val sampleInvoice = InvoiceAmtDto(1L, "INV001", 1000.0)
        val csvContent = createChequeCsvContent(
            partnerDetailId = "123",
            invoiceNo = "INV001",
            amount = "1000.00",
            chequeNo = "CHQ001",
            chequeDate = "01/01/2024",
            bankName = "Test Bank",
            paymentDate = "01/01/2024"
        )
        val banksField = SettlementFileValidator::class.java.getDeclaredField("banks")
        banksField.isAccessible = true
        banksField.set(settlementFileValidator, listOf(sampleBank))
        whenever(s3Uploader.secureUploadWithKey(any<ByteArray>(), any<FileType>(), any<Long>(), any<String>(), anyOrNull())).thenReturn("test-uri")
        whenever(s3Uploader.read(any(), anyOrNull())).thenReturn(ByteArrayInputStream(csvContent.toByteArray()))
        whenever(settlementTaskRepo.findById(any())).thenReturn(Optional.of(sampleSettlementTask))
        whenever(invoiceRepo.getAllPartnerInvoiceOverDueAmount(any(), any(), any())).thenReturn(1000.0)
        whenever(invoiceRepo.getVendorInvoiceData(any(), any(), any(), any())).thenReturn(PageImpl(listOf(sampleInvoice)))

        // When
        settlementFileValidator.validateFile(
            objectKey = "test-key",
            settlementTaskId = 1L,
            ds = null
        )

        // Then
        verify(settlementTaskItemRepo).saveAll(any<List<SettlementTaskItem>>())
        verify(settlementTaskRepo).save(argThat<SettlementTask> { task ->
            task.fileStatus == SettlementFileStatus.VALIDATION_PASSED &&
            task.taskStatus == SettlementTaskStatus.PENDING
        })
    }

    @Test
    fun `validateFile should validate cash file successfully`() {
        // Given
        val sampleInvoice = InvoiceAmtDto(1L, "INV001", 1000.0)
        sampleSettlementTask.settlementMode = PaymentType.CASH
        val csvContent = createCashCsvContent(
            partnerDetailId = "123",
            invoiceNo = "INV001",
            amount = "1000.00",
            refNo = "REF001",
            paymentDate = "01/01/2024"
        )
        whenever(s3Uploader.secureUploadWithKey(any<ByteArray>(), any<FileType>(), any<Long>(), any<String>(), anyOrNull())).thenReturn("test-uri")
        whenever(s3Uploader.read(any(), anyOrNull())).thenReturn(ByteArrayInputStream(csvContent.toByteArray()))
        whenever(settlementTaskRepo.findById(any())).thenReturn(Optional.of(sampleSettlementTask))
        whenever(invoiceRepo.getAllPartnerInvoiceOverDueAmount(any(), any(), any())).thenReturn(1000.0)
        whenever(invoiceRepo.getVendorInvoiceData(any(), any(), any(), any())).thenReturn(PageImpl(listOf(sampleInvoice)))
        // When
        settlementFileValidator.validateFile(
            objectKey = "test-key",
            settlementTaskId = 1L,
            ds = null
        )

        // Then
        verify(settlementTaskItemRepo).saveAll(any<List<SettlementTaskItem>>())
        verify(settlementTaskRepo).save(argThat<SettlementTask> { task ->
            task.fileStatus == SettlementFileStatus.VALIDATION_PASSED &&
                    task.taskStatus == SettlementTaskStatus.PENDING
        })
    }

    @Test
    fun `validateFile should handle invalid partner detail id`() {
        // Given
        val csvContent = createChequeCsvContent(
            partnerDetailId = "invalid",
            invoiceNo = "INV001",
            amount = "1000.00",
            chequeNo = "CHQ001",
            chequeDate = "01/01/2024",
            bankName = "Test Bank",
            paymentDate = "01/01/2024"
        )
        whenever(s3Uploader.secureUploadWithKey(any<ByteArray>(), any<FileType>(), any<Long>(), any<String>(), anyOrNull())).thenReturn("test-uri")
        whenever(s3Uploader.read(any(), anyOrNull())).thenReturn(ByteArrayInputStream(csvContent.toByteArray()))
        whenever(settlementTaskRepo.findById(any())).thenReturn(Optional.of(sampleSettlementTask))

        // When
        settlementFileValidator.validateFile(
            objectKey = "test-key",
            settlementTaskId = 1L,
            ds = null
        )

        // Then
        verify(settlementTaskItemRepo, never()).saveAll(any<List<SettlementTaskItem>>())
        verify(settlementTaskRepo).save(argThat<SettlementTask> { task ->
            task.fileStatus == SettlementFileStatus.VALIDATION_FAILED &&
            task.taskStatus == SettlementTaskStatus.FAILED
        })
    }

    @Test
    fun `validateFile should handle invalid bank name`() {
        // Given
        val csvContent = createChequeCsvContent(
            partnerDetailId = "123",
            invoiceNo = "INV001",
            amount = "1000.00",
            chequeNo = "CHQ001",
            chequeDate = "01/01/2024",
            bankName = "Invalid Bank",
            paymentDate = "01/01/2024"
        )
        whenever(s3Uploader.secureUploadWithKey(any<ByteArray>(), any<FileType>(), any<Long>(), any<String>(), anyOrNull())).thenReturn("test-uri")
        whenever(s3Uploader.read(any(), anyOrNull())).thenReturn(ByteArrayInputStream(csvContent.toByteArray()))
        whenever(settlementTaskRepo.findById(any())).thenReturn(Optional.of(sampleSettlementTask))

        // When
        settlementFileValidator.validateFile(
            objectKey = "test-key",
            settlementTaskId = 1L,
            ds = null
        )

        // Then
        verify(settlementTaskItemRepo, never()).saveAll(any<List<SettlementTaskItem>>())
        verify(settlementTaskRepo).save(argThat<SettlementTask> { task ->
            task.fileStatus == SettlementFileStatus.VALIDATION_FAILED &&
            task.taskStatus == SettlementTaskStatus.FAILED
        })
    }

    @Test
    fun `validateFile should handle future payment date`() {
        // Given
        val futureDate = LocalDate.now().plusDays(1).format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        val csvContent = createChequeCsvContent(
            partnerDetailId = "123",
            invoiceNo = "INV001",
            amount = "1000.00",
            chequeNo = "CHQ001",
            chequeDate = "01/01/2024",
            bankName = "Test Bank",
            paymentDate = futureDate
        )
        whenever(s3Uploader.secureUploadWithKey(any<ByteArray>(), any<FileType>(), any<Long>(), any<String>(), anyOrNull())).thenReturn("test-uri")
        whenever(s3Uploader.read(any(), anyOrNull())).thenReturn(ByteArrayInputStream(csvContent.toByteArray()))
        whenever(settlementTaskRepo.findById(any())).thenReturn(Optional.of(sampleSettlementTask))

        // When
        settlementFileValidator.validateFile(
            objectKey = "test-key",
            settlementTaskId = 1L,
            ds = null
        )

        // Then
        verify(settlementTaskItemRepo, never()).saveAll(any<List<SettlementTaskItem>>())
        verify(settlementTaskRepo).save(argThat<SettlementTask> { task ->
            task.fileStatus == SettlementFileStatus.VALIDATION_FAILED &&
            task.taskStatus == SettlementTaskStatus.FAILED
        })
    }

    @Test
    fun `validateFile should handle empty file`() {
        // Given
        whenever(s3Uploader.secureUploadWithKey(any<ByteArray>(), any<FileType>(), any<Long>(), any<String>(), anyOrNull())).thenReturn("test-uri")
        whenever(s3Uploader.read(any(), anyOrNull())).thenReturn(ByteArrayInputStream("".toByteArray()))
        whenever(settlementTaskRepo.findById(1L)).thenReturn(Optional.of(sampleSettlementTask))
        // When
        settlementFileValidator.validateFile(
            objectKey = "test-key",
            settlementTaskId = 1L,
            ds = null
        )

        // Then
        verify(settlementTaskItemRepo, never()).saveAll(any<List<SettlementTaskItem>>())
        verify(settlementTaskRepo).save(argThat<SettlementTask> { task ->
            task.fileStatus == SettlementFileStatus.VALIDATION_FAILED &&
            task.taskStatus == SettlementTaskStatus.FAILED
        })
    }

    @Test
    fun `validateFile should handle task not found`() {
        // Given
        whenever(settlementTaskRepo.findById(any())).thenReturn(Optional.empty())

        // When/Then
        assertThrows(RequestException::class.java) {
            settlementFileValidator.validateFile(
                objectKey = "test-key",
                settlementTaskId = 1L,
                ds = null
            )
        }
    }

    private fun createChequeCsvContent(
        partnerDetailId: String,
        invoiceNo: String,
        amount: String,
        chequeNo: String,
        chequeDate: String,
        bankName: String,
        paymentDate: String
    ): String {
        val output = ByteArrayOutputStream()
        val printer = CSVPrinter(output.bufferedWriter(), CSVFormat.DEFAULT.withHeader(*SettlementTaskUtils.chequeSettlementFileCols.toTypedArray()))
        printer.printRecord(partnerDetailId, invoiceNo, amount, chequeNo, chequeDate, bankName, paymentDate, amount)
        printer.flush()
        return output.toString()
    }

    private fun createCashCsvContent(
        partnerDetailId: String,
        invoiceNo: String,
        amount: String,
        refNo: String,
        paymentDate: String
    ): String {
        val output = ByteArrayOutputStream()
        val printer = CSVPrinter(output.bufferedWriter(), CSVFormat.DEFAULT.withHeader(*SettlementTaskUtils.neftOrCashSettlementFileCols.toTypedArray()))
        printer.printRecord(partnerDetailId, invoiceNo, amount, refNo, paymentDate, amount)
        printer.flush()
        return output.toString()
    }

    private fun createSampleInvoice(): BkInvoice {
        return BkInvoice(
            invoiceNum = "INV001",
            amount = 1000.0,
            paidAmount = 0.0,
            status = InvoiceStatus.PENDING,
            id = 1,
            createdOn = LocalDateTime.now(),
            updatedOn = LocalDateTime.now(),
            createdBy = "SYSTEM",
            updatedBy = "SYSTEM",
            invoiceId = "INV001",
            supplierId = 1,
            supplierName = "TEST",
            dueDate = LocalDate.now(),
            items = mutableListOf(),
            settledOn = null,
            purchaseType = null,
            partnerId = 1,
            partnerDetailId = 1,
            tenant = "th124",
            type = PartnerType.CUSTOMER,
            client = InvoiceType.RIO,
            settlementId = null,
            settlementNumber = null,
            gatePassInvoice = null,
            tcsAmount = null,
            tcs = null,
            distributorPdi = 1,
            apiVersion = APIVersionType.V1,
            parentTenant = null,
            isMigrated = false
        )
    }

} 