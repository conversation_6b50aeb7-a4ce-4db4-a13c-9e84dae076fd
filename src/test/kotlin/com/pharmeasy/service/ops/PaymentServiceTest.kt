package com.pharmeasy.service.ops

import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.PaymentTransactionUpdates
import com.pharmeasy.model.PaymentTransactionUpdatesData
import com.pharmeasy.model.Supplier
import com.pharmeasy.model.ops.PaymentBatchRequest
import com.pharmeasy.model.ops.RIOPaymentDTO
import com.pharmeasy.proxy.CoreServiceProxy
import com.pharmeasy.proxy.SupplierProxy
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.InvoiceService
import com.pharmeasy.service.ReceiptOrchestratorService
import com.pharmeasy.service.TradeCreditPaymentService
import com.pharmeasy.type.CreationType
import com.pharmeasy.type.PaymentTransactionStatus
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.ops.PaymentPartnerType
import com.pharmeasy.util.EventPublisherUtil
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class PaymentServiceTest {

    @MockK
    private lateinit var receiptOrchestratorService: ReceiptOrchestratorService

    @MockK
    private lateinit var supplierProxy: SupplierProxy

    @MockK
    private lateinit var coreServiceProxy: CoreServiceProxy

    @MockK
    private lateinit var invoiceService: InvoiceService

    @MockK
    private lateinit var companyService: CompanyService

    @MockK
    private lateinit var eventPublisherUtil: EventPublisherUtil

    @MockK
    private lateinit var tradeCreditPaymentService: TradeCreditPaymentService

    @InjectMockKs
    private lateinit var paymentService: PaymentService

    private lateinit var validRioPaymentDTO: RIOPaymentDTO
    private lateinit var tradeCreditRioPaymentDTO: RIOPaymentDTO
    private lateinit var mockSupplier: Supplier

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        
        validRioPaymentDTO = RIOPaymentDTO(
            retailerTxnId = "TXN123",
            retailerTxnDate = 1640995200000L, // 2022-01-01
            retailerName = "Test Retailer",
            retailerPartyCode = "PARTY123",
            retailerFrontEndPartyCode = "12345",
            distributorInvoiceId = "INV123",
            distributorInvoiceAmount = 1000.0,
            distributorInvoiceOutstandingAmount = 500.0,
            retailerTxnAmount = 500.0,
            distributorInvoiceDueDate = 1641081600000L, // 2022-01-02
            retailerTxnType = "CASH",
            initiatedBy = "RIO_DELIVERY",
            retailerTxnStatus = "SUCCESS",
            advanceAmount = 0.0,
            paymentMode = "DIGITAL_RECEIPT",
            invoicePrefix = "INV",
            distributorInvoiceDate = 1640995200000L,
            category = "PAYMENT",
            invoiceNumber = "INV123456",
            salesmanName = "John Doe",
            salesmanId = "SALES123",
            chequeNo = null,
            chequeDate = null,
            neftId = null,
            bankName = null,
            retailerTotalTxnAmount = 500.0,
            distributorId = 1001L,
            type = CreationType.INVOICE,
            creditTransactionId = null,
            creditDueDate = null,
            creditPartner = null,
            isBankDeposit = false,
            bankDepositSlipNo = null
        )

        tradeCreditRioPaymentDTO = validRioPaymentDTO.copy(
            retailerTxnId = "TXN124",
            category = "CREDIT_REPAYMENT",
            creditTransactionId = "CREDIT123",
            creditDueDate = 1641168000000L, // 2022-01-03
            creditPartner = PaymentPartnerType.TRADE_CREDIT
        )

        mockSupplier = Supplier(
            partnerId = 1001L,
            partnerName = "Test Supplier",
            partnerDetailId = 12345L
        )
    }

    @Test
    fun `processRioPaymentV2 should process valid payments successfully`() {
        // Given
        val rioPaymentDTOList = listOf(validRioPaymentDTO)
        val tenant = "test-tenant"

        every { supplierProxy.supplier(null, 12345L) } returns listOf(mockSupplier)
        every { coreServiceProxy.getRetailerByPartnerDetailId(12345L) } returns mockk {
            every { tenant } returns tenant
        }
        every { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) } just Runs
        every { eventPublisherUtil.sendPaymentTransactionUpdates(any()) } just Runs

        // When
        paymentService.processRioPaymentV2(rioPaymentDTOList)

        // Then
        verify(exactly = 1) { supplierProxy.supplier(null, 12345L) }
        verify(exactly = 1) { coreServiceProxy.getRetailerByPartnerDetailId(12345L) }
        verify(exactly = 1) { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) }
        verify(exactly = 1) { 
            eventPublisherUtil.sendPaymentTransactionUpdates(
                PaymentTransactionUpdatesData(
                    listOf(PaymentTransactionUpdates("TXN123", PaymentTransactionStatus.PROCESSED))
                )
            )
        }
    }

    @Test
    fun `processRioPaymentV2 should handle trade credit repayments separately`() {
        // Given
        val rioPaymentDTOList = listOf(validRioPaymentDTO, tradeCreditRioPaymentDTO)
        val tenant = "test-tenant"

        every { supplierProxy.supplier(null, 12345L) } returns listOf(mockSupplier)
        every { coreServiceProxy.getRetailerByPartnerDetailId(12345L) } returns mockk {
            every { tenant } returns tenant
        }
        every { tradeCreditPaymentService.updateTradeCreditPayment(any()) } just Runs
        every { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) } just Runs
        every { eventPublisherUtil.sendPaymentTransactionUpdates(any()) } just Runs

        // When
        paymentService.processRioPaymentV2(rioPaymentDTOList)

        // Then
        verify(exactly = 1) { tradeCreditPaymentService.updateTradeCreditPayment(any()) }
        verify(exactly = 1) { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) }
        verify(exactly = 1) { 
            eventPublisherUtil.sendPaymentTransactionUpdates(
                PaymentTransactionUpdatesData(
                    listOf(
                        PaymentTransactionUpdates("TXN123", PaymentTransactionStatus.PROCESSED),
                        PaymentTransactionUpdates("TXN124", PaymentTransactionStatus.PROCESSED)
                    )
                )
            )
        }
    }

    @Test
    fun `processRioPaymentV2 should throw exception when partner not found`() {
        // Given
        val rioPaymentDTOList = listOf(validRioPaymentDTO)

        every { supplierProxy.supplier(null, 12345L) } returns emptyList()

        // When & Then
        val exception = assertThrows<RequestException> {
            paymentService.processRioPaymentV2(rioPaymentDTOList)
        }

        assert(exception.message == "Partner not found")
        verify(exactly = 1) { supplierProxy.supplier(null, 12345L) }
        verify(exactly = 0) { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) }
    }

    @Test
    fun `processRioPaymentV2 should throw exception when tenant not found`() {
        // Given
        val rioPaymentDTOList = listOf(validRioPaymentDTO)

        every { supplierProxy.supplier(null, 12345L) } returns listOf(mockSupplier)
        every { coreServiceProxy.getRetailerByPartnerDetailId(12345L) } returns null

        // When & Then
        val exception = assertThrows<RequestException> {
            paymentService.processRioPaymentV2(rioPaymentDTOList)
        }

        assert(exception.message!!.contains("Tenant not found"))
        verify(exactly = 1) { supplierProxy.supplier(null, 12345L) }
        verify(exactly = 1) { coreServiceProxy.getRetailerByPartnerDetailId(12345L) }
        verify(exactly = 0) { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) }
    }

    @Test
    fun `processRioPaymentV2 should throw exception for invalid request - missing retailerTxnId`() {
        // Given
        val invalidPayment = validRioPaymentDTO.copy(retailerTxnId = null)
        val rioPaymentDTOList = listOf(invalidPayment)

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            paymentService.processRioPaymentV2(rioPaymentDTOList)
        }

        assert(exception.message == "Transaction amount is required")
        verify(exactly = 0) { supplierProxy.supplier(any(), any()) }
    }

    @Test
    fun `processRioPaymentV2 should throw exception for invalid request - missing partnerDetailId`() {
        // Given
        val invalidPayment = validRioPaymentDTO.copy(retailerFrontEndPartyCode = null)
        val rioPaymentDTOList = listOf(invalidPayment)

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            paymentService.processRioPaymentV2(rioPaymentDTOList)
        }

        assert(exception.message == "Partner Detail Id is required")
        verify(exactly = 0) { supplierProxy.supplier(any(), any()) }
    }

    @Test
    fun `processRioPaymentV2 should throw exception for invalid request - missing distributorId`() {
        // Given
        val invalidPayment = validRioPaymentDTO.copy(distributorId = null)
        val rioPaymentDTOList = listOf(invalidPayment)

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            paymentService.processRioPaymentV2(rioPaymentDTOList)
        }

        assert(exception.message == "Distributor ID is required")
        verify(exactly = 0) { supplierProxy.supplier(any(), any()) }
    }

    @Test
    fun `processRioPaymentV2 should throw exception for invalid transaction amount`() {
        // Given
        val invalidPayment = validRioPaymentDTO.copy(
            retailerTxnAmount = 1000.0,
            retailerTotalTxnAmount = 500.0
        )
        val rioPaymentDTOList = listOf(invalidPayment)

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            paymentService.processRioPaymentV2(rioPaymentDTOList)
        }

        assert(exception.message == "Retailer transaction amount must be less than or equal to the total transaction amount")
        verify(exactly = 0) { supplierProxy.supplier(any(), any()) }
    }

    @Test
    fun `processRioPaymentV2 should throw exception for negative transaction amount with invoice`() {
        // Given
        val invalidPayment = validRioPaymentDTO.copy(retailerTxnAmount = -100.0)
        val rioPaymentDTOList = listOf(invalidPayment)

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            paymentService.processRioPaymentV2(rioPaymentDTOList)
        }

        assert(exception.message == "Transaction amount must be positive")
        verify(exactly = 0) { supplierProxy.supplier(any(), any()) }
    }

    @Test
    fun `processRioPaymentV2 should process only trade credit repayments when all are trade credit`() {
        // Given
        val tradeCreditPayments = listOf(tradeCreditRioPaymentDTO)
        val tenant = "test-tenant"

        every { supplierProxy.supplier(null, 12345L) } returns listOf(mockSupplier)
        every { coreServiceProxy.getRetailerByPartnerDetailId(12345L) } returns mockk {
            every { tenant } returns tenant
        }
        every { tradeCreditPaymentService.updateTradeCreditPayment(any()) } just Runs
        every { eventPublisherUtil.sendPaymentTransactionUpdates(any()) } just Runs

        // When
        paymentService.processRioPaymentV2(tradeCreditPayments)

        // Then
        verify(exactly = 1) { tradeCreditPaymentService.updateTradeCreditPayment(any()) }
        verify(exactly = 1) { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) }
        verify(exactly = 1) { eventPublisherUtil.sendPaymentTransactionUpdates(any()) }
    }

    @Test
    fun `processRioPaymentV2 should handle empty payment list gracefully`() {
        // Given
        val emptyList = emptyList<RIOPaymentDTO>()

        // When & Then
        val exception = assertThrows<NoSuchElementException> {
            paymentService.processRioPaymentV2(emptyList)
        }

        verify(exactly = 0) { supplierProxy.supplier(any(), any()) }
        verify(exactly = 0) { receiptOrchestratorService.processPayment(any<PaymentBatchRequest>()) }
    }
}
