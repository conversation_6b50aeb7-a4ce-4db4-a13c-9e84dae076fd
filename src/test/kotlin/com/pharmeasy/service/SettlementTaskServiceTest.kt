package com.pharmeasy.service

import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.repo.SettlementTaskRepo
import com.pharmeasy.repo.read.SettlementTaskReadRepo
import com.pharmeasy.stream.BulkSettlementPusher
import com.pharmeasy.type.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.LocalDateTime
import java.util.*
import org.junit.jupiter.api.Assertions.*
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyString

@ExtendWith(MockitoExtension::class)
class SettlementTaskServiceTest {

    @Mock private lateinit var settlementTaskRepo: SettlementTaskRepo
    @Mock private lateinit var settlementTaskReadRepo: SettlementTaskReadRepo
    @Mock private lateinit var companyService: CompanyService
    @Mock private lateinit var bulkSettlementPusher: BulkSettlementPusher
    @Mock private lateinit var s3FileUtilityService: S3FileUtilityService
    @Mock private lateinit var s3Uploader: S3Uploader

    private lateinit var settlementTaskService: SettlementTaskService

    private lateinit var sampleCompanyTenantMapping: CompanyTenantMapping
    private lateinit var sampleSettlementTask: SettlementTask
    private lateinit var sampleSettlementFileMeta: SettlementFileMeta
    private lateinit var sampleProcessSettlementTaskDto: ProcessSettlementTaskDto
    private lateinit var samplePagination: PaginationDto
    @BeforeEach
    fun setUp() {
        settlementTaskService = SettlementTaskService(
            settlementTaskRepo = settlementTaskRepo,
            settlementTaskReadRepo = settlementTaskReadRepo,
            companyService = companyService,
            bulkSettlementPusher = bulkSettlementPusher,
            s3FileUtilityService = s3FileUtilityService,
            s3Uploader = s3Uploader,
            bucket = "test-bucket",
            settlementPrefix = "test-prefix"
        )

        sampleCompanyTenantMapping = CompanyTenantMapping(
            id = 1L,
            companyId = 456L,
            tenant = "test_tenant",
            tenantName = "test_tenant",
            theaId = 1,
            partnerDetailId = 123L
        )

        sampleSettlementTask = SettlementTask(
            id = 1L,
            createdOn = LocalDateTime.now(),
            createdBy = "test_user",
            settlementMode = PaymentType.CHEQUE,
            fileStatus = SettlementFileStatus.VALIDATION_IN_PROGRESS,
            taskStatus = SettlementTaskStatus.PENDING,
            s3Uri = "test-uri",
            partnerType = PartnerType.VENDOR,
            company = sampleCompanyTenantMapping,
            isPartnerLevel = false
        )

        sampleSettlementFileMeta = SettlementFileMeta(
            objectKey = "test-uri",
            mode = PaymentType.CHEQUE,
            isPartnerLevel = false
        )

        sampleProcessSettlementTaskDto = ProcessSettlementTaskDto(
            taskId = 1L,
            selectionType = SelectionType.ALL,
            taskItemIdList = null,
            settlementMode = PaymentType.CASH
        )

        samplePagination = PaginationDto(
            elements = 1L,
            page = 1,
            hasPrevious = false,
            hasNext = false,
            data = listOf(sampleSettlementTask)
        )

    }

    @Test
    fun `uploadSettlementFile should throw exception when company not found`() {
        // Given
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(null)

        // When/Then
        assertThrows(RequestException::class.java) {
            settlementTaskService.uploadSettlementFile(
                settlementFileMeta = sampleSettlementFileMeta,
                tenant = "test_tenant",
                user = "test_user",
                ds = null,
                customerType = false
            )
        }
    }


    @Test
    fun `processSettlementTask should throw exception when task not found`() {
        // Given
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(sampleCompanyTenantMapping)
        whenever(settlementTaskRepo.findById(any())).thenReturn(Optional.empty())

        // When/Then
        assertThrows(RequestException::class.java) {
            settlementTaskService.processSettlementTask(
                processTaskDto = sampleProcessSettlementTaskDto,
                tenant = "test_tenant",
                user = "test_user",
                ds = null,
                userHash = "test_hash"
            )
        }
    }

    @Test
    fun `getTasks should throw exception when company not found`() {
        // Given
        whenever(companyService.getCompanyTenantMappingObject("test_tenant")).thenReturn(null)

        // When/Then
        assertThrows(RequestException::class.java) {
            settlementTaskService.getTasks(
                fromDateTime = null,
                toDateTime = null,
                mode = null,
                status = null,
                customerType = false,
                ds = null,
                tenant = "test_tenant",
                page = 0,
                size = 10,
                isPartnerLevel = null
            )
        }
    }
} 