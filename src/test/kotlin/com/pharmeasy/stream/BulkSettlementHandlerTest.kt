package com.pharmeasy.stream

import com.pharmeasy.data.BulkSettlementFileValidationDto
import com.pharmeasy.data.ops.BulkSettlementFileProcess
import com.pharmeasy.model.ProcessSettlementTaskDto
import com.pharmeasy.service.SettlementFileValidator
import com.pharmeasy.service.SettlementTaskProcessor
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SelectionType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import org.junit.jupiter.api.Assertions.*

@ExtendWith(MockitoExtension::class)
class BulkSettlementHandlerTest {

    @Mock private lateinit var settlementFileValidator: SettlementFileValidator
    @Mock private lateinit var settlementTaskProcessor: SettlementTaskProcessor

    private lateinit var bulkSettlementHandler: BulkSettlementHandler

    private lateinit var sampleValidationDto: BulkSettlementFileValidationDto
    private lateinit var sampleProcessDto: BulkSettlementFileProcess

    @BeforeEach
    fun setUp() {
        // Instantiate and inject mocks manually
        bulkSettlementHandler = BulkSettlementHandler().apply {
            // Use setter injection to avoid reflection
            val validatorField = BulkSettlementHandler::class.java.getDeclaredField("settlementFileValidator")
            validatorField.isAccessible = true
            validatorField.set(this, settlementFileValidator)

            val processorField = BulkSettlementHandler::class.java.getDeclaredField("settlementTaskProcessor")
            processorField.isAccessible = true
            processorField.set(this, settlementTaskProcessor)
        }

        sampleValidationDto = BulkSettlementFileValidationDto(
            key = "test-uri",
            taskId = 1L,
            ds = null
        )

        sampleProcessDto = BulkSettlementFileProcess(
            processSettlementTaskDto = ProcessSettlementTaskDto(
                settlementMode = PaymentType.CASH,
                taskId = 1L,
                taskItemIdList = listOf(100L, 200L),
                selectionType = SelectionType.ALL
            ),
            user = "test_user",
            ds = "ds1",
            tenant = "test_tenant",
            userHash = "test_hash"
        )
    }

    @Test
    fun `bulkSettlementFileValidationConsumer should handle validation successfully`() {
        // Given
        doNothing().whenever(settlementFileValidator).validateFile("test-uri", 1L, null)

        // When
        bulkSettlementHandler.bulkSettlementFileValidationConsumer(sampleValidationDto)

        // Then
        verify(settlementFileValidator, times(1)).validateFile(
            sampleValidationDto.key,
            sampleValidationDto.taskId,
            sampleValidationDto.ds
        )
    }

    @Test
    fun `bulkSettlementFileProcessConsumer should handle process successfully`() {
        // Given
        doNothing().whenever(settlementTaskProcessor).processTask(any(), any(), any(), any(), any())

        // When
        bulkSettlementHandler.bulkSettlementFileProcessConsumer(sampleProcessDto)

        // Then
        verify(settlementTaskProcessor, times(1)).processTask(
            sampleProcessDto.processSettlementTaskDto,
            sampleProcessDto.user,
            sampleProcessDto.ds,
            sampleProcessDto.tenant,
            sampleProcessDto.userHash
        )
    }
}