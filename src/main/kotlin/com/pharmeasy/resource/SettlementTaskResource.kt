package com.pharmeasy.resource

import com.pharmeasy.model.*
import com.pharmeasy.service.*
import com.pharmeasy.type.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("settlement/bulk")
class SettlementTaskResource {
    @Autowired
    private lateinit var settlementTaskService: SettlementTaskService

    @GetMapping("uploadUrl/{mode}")
    fun getFileUploadUrl(@PathVariable mode: PaymentType): UrlDataDTO {
        return settlementTaskService.getFileUploadUrl(mode)
    }

    @PostMapping("upload")
    fun uploadSettlementFile(@RequestBody settlementFileMeta: SettlementFileMeta, @RequestParam tenant: String,
                             @RequestParam user: String, @RequestParam(required = false) ds: String? = null,
                             @RequestParam(required = false) customerType: Boolean = false): PaginationDto {
        return settlementTaskService.uploadSettlementFile(settlementFileMeta, tenant, user, ds, customerType)
    }

    @PostMapping("process")
    fun processSettlementTask(@RequestBody processTaskDto: ProcessSettlementTaskDto, @RequestParam tenant: String,
                              @RequestParam user: String, @RequestParam(required = false) ds: String? = null, @RequestParam userHash: String): PaginationDto {
        return settlementTaskService.processSettlementTask(processTaskDto, tenant, user, ds, userHash)
    }

    @GetMapping("summary/{taskId}")
    fun getSettlementTaskSummary(@PathVariable taskId: Long): SettlementTaskSummary {
        return settlementTaskService.getSettlementTaskSummary(taskId)
    }

    @GetMapping("detail/{taskId}")
    fun getSettlementTaskItems(@PathVariable taskId: Long,
                               @RequestParam(required = false) page: Int?,
                               @RequestParam(required = false) size: Int?,
                               @RequestParam(required = false) invoiceNo: String?): PaginationDto {
        return settlementTaskService.getSettlementTaskItems(taskId, page, size, invoiceNo)
    }

    @GetMapping("download/{taskId}")
    fun downloadSettlementTaskItems(@PathVariable taskId: Long): CreateResultData {
        return settlementTaskService.downloadSettlementTaskItems(taskId)
    }

    @PostMapping("cancel/{taskId}")
    fun cancelSettlementTask(@PathVariable taskId: Long, @RequestParam tenant: String): PaginationDto {
        return settlementTaskService.cancelSettlementTask(taskId, tenant)
    }

    @GetMapping("task")
    fun getSettlementTasks(@RequestParam(required = false) from: LocalDate? = null,
                           @RequestParam(required = false) to: LocalDate? = null,
                           @RequestParam(required = false) mode: PaymentType? = null,
                           @RequestParam(required = false) status: SettlementTaskStatus? = null,
                           @RequestParam(required = false) customerType: Boolean = false,
                           @RequestParam tenant: String,
                           @RequestParam(required = false) ds: String? = null,
                           @RequestParam(required = false) page: Int?,
                           @RequestParam(required = false) size: Int?,
                           @RequestParam(required = false) isPartnerLevel: Boolean?): PaginationDto {
        val fromDateTime = from?.atTime(0,0,0)
        val toDateTime = to?.atTime(23,59,59)

        return settlementTaskService.getTasks(fromDateTime, toDateTime, mode, status, customerType, ds, tenant, page, size,isPartnerLevel)
    }


}