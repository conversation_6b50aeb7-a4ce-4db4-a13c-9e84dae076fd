package com.pharmeasy.resource

import com.pharmeasy.annotation.Get
import com.pharmeasy.annotation.Post
import com.pharmeasy.annotation.Put
import com.pharmeasy.data.DebitNote
import com.pharmeasy.exception.BookkeeperErrors
import com.pharmeasy.exception.DetailedRequestException
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.*
import com.pharmeasy.service.CompanyService
import com.pharmeasy.service.DebitNoteService
import com.pharmeasy.stream.DebitNoteHandler
import com.pharmeasy.type.InvoiceType
import com.pharmeasy.type.NoteStatus
import com.pharmeasy.type.NoteTypes
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.time.LocalDate


@RestController
@RequestMapping("/debitnotes")
class DebitNoteResource {
    companion object {
        private val log = LoggerFactory.getLogger(DebitNoteResource::class.java)
    }

    @Autowired
    private lateinit var debitNoteService: DebitNoteService

    @Autowired
    private lateinit var companyService: CompanyService

    @Autowired
    private lateinit var debitNoteHandler: DebitNoteHandler

    @Get("/getBySupplier")
    fun getDebitNotesBySupplierId(@RequestParam(required = true) supplierId: Long,
                                  @RequestParam(required = false) status: NoteStatus?,
                                  @RequestParam(required = false) type: NoteTypes?,
                                  @RequestParam(required = false) customerType: Boolean?,
                                  @RequestParam(required = true) tenant: String,
                                  @RequestParam(required = false) ds: String? = null):
            ResponseEntity<List<DebitNote> > {

        val dn = debitNoteService.getDebitNotesForSupplier(supplierId, status, type, tenant,customerType?:false,ds)
                ?: throw RequestException("DebitNotes not found")

        return ResponseEntity.ok(dn)
    }

    @Get("/{id}")
    fun getDebitNoteById(@PathVariable("id") id: Long):
            ResponseEntity<DebitNote> {

        val dn = debitNoteService.get(id) ?: throw RequestException("DebitNote not found")
        return ResponseEntity.ok(dn)
    }

    @Get
    fun getDebitNotes(@RequestParam(required = false) page: Int?,
                      @RequestParam(required = false) size: Int?,
                      @RequestParam(required = false) supplierId: Long?,
                      @RequestParam(required = false) invoiceNum: String?,
                      @RequestParam(required = false) debitNoteId: Long?,
                      @RequestParam(required = false) status: NoteStatus?,
                      @RequestParam(required = false) type: NoteTypes?,
                      @RequestParam(required = false) customerType: Boolean?,
                      @RequestParam(required = false) tenant: String?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                      @RequestParam(required = false) ds: String? = null): PageableDebitNote {

        var tenantNew: String? = tenant
        if (tenant.isNullOrEmpty()) tenantNew = null
        return  debitNoteService.getDebitNotes(page, size, supplierId, invoiceNum, debitNoteId, status, type, tenantNew, from, to,customerType?:false,ds)
    }

    @Get("/vendor/{id}")
    fun getVendorDebitNotes(@RequestParam("page", required = false) page: Int?,
                            @RequestParam(required = false) size: Int?,
                            @PathVariable("id") id: Long,
                            @RequestParam(required = false) debitNoteNumber: String?,
                            @RequestParam(required = false) creditNoteNumber: String?,
                            @RequestParam(required = false) invoiceNum: String?,
                            @RequestParam(required = false) invoiceId: String?,
                            @RequestParam(required = false) status: NoteStatus?,
                            @RequestParam(required = false) customerType: Boolean?,
                            @RequestParam(required = false) type: NoteTypes?,
                            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
                            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
                            @RequestParam(required = false) source: String?,
                            @RequestParam tenant: String,
                            @RequestParam(required = false) ds: String? = null): PaginationDto {

        var sourceNew: String? = source
        if (source.isNullOrEmpty()) sourceNew = null
        var invNum = invoiceNum
        if (invoiceNum.isNullOrEmpty()) invNum = null
        var dnNum: String? = debitNoteNumber
        if (debitNoteNumber.isNullOrEmpty()) dnNum = null
        var cnNum: String? = creditNoteNumber
        if (creditNoteNumber.isNullOrEmpty()) cnNum = null

        return debitNoteService.getVendorDebitNotes(page, size, id, dnNum, cnNum, invNum, status, type, from,to, sourceNew, tenant,customerType?:false,ds,invoiceId)
    }

    @Post
    fun save(@RequestHeader("x-user") user: String,
             @RequestBody debitNote: DebitNote,
             @RequestParam(required = false) ds: String? = null): ResponseEntity<Any> {
        if(ds!=null){
            debitNote.tenant = companyService.getCompanyTenantMappingObject(ds?:debitNote.tenant)?.tenant ?: debitNote.tenant

        }

        validate(debitNote)
        return ResponseEntity.ok(debitNoteService.save(user, debitNote,null))
    }

    @Put("/{id}")
    fun update(@RequestHeader("x-user") user: String,
               @PathVariable("id") id: Long,
               @RequestBody debitNote: DebitNote): ResponseEntity<Any> {

        validate(debitNote)
        return ResponseEntity.ok(debitNoteService.update(user, id, debitNote))
    }

    @Put
    fun closeAll(@RequestHeader("x-user") user: String,
                  @RequestBody debitNoteReason: DebitNoteReason): ResponseEntity<Any> {

        return ResponseEntity.ok(debitNoteService.closeAll(user, debitNoteReason.reason, debitNoteReason.debitNotes))
    }

    @Post("/purchaseReturns")
    fun retryCreateDebitNotes(@RequestBody purchaseReturnDebitNoteDto: PurchaseReturnDebitNoteDto): ResponseEntity<Any>? {
        return debitNoteService.createDebitNotes(purchaseReturnDebitNoteDto)?.let { ResponseEntity.ok(it) }
    }

    fun validate(debitNote: DebitNote) {

        if (debitNote.supplierId == 0L)
            throw DetailedRequestException(BookkeeperErrors.MISSING_INPUT, arrayOf("Supplier Id"))

        if (debitNote.amountReceivable < BigDecimal(0))
            throw DetailedRequestException(BookkeeperErrors.INVALID_INPUT, arrayOf("Amount Receivable"))

        if (debitNote.amountReceived > debitNote.amountReceivable)
            throw DetailedRequestException(BookkeeperErrors.INVALID_INPUT, arrayOf("Amount received"))
    }

    @Get("/vendor/all")
    fun getVendorDebitNoteData(@RequestParam("page", required = false) page: Int?,
                               @RequestParam(required = false) size: Int?,
                               @RequestParam("partnerIds") partnerIds: List<Long>?,
                               @RequestParam(required = false) partnerId: Long?,
                               @RequestParam(required = false) partnerDetailId: Long?,
                               @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startAt: LocalDate?,
                               @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endAt: LocalDate?,
                               @RequestParam(required = false) customerType: Boolean?,
                               @RequestParam("noteType") noteType: NoteTypes?,
                               @RequestParam("tenant") tenant: String,
                               @RequestParam(required = false) onClick: Boolean?,
                               @RequestParam(required = false) ds: String? = null,
                               @RequestParam(required = false) firmType: List<Int>?,
                               @RequestParam ("client") client: List<InvoiceType>): PaginationDto {
        return debitNoteService.getVendorDebitNotesData(partnerIds, partnerId,partnerDetailId, startAt, endAt, noteType, tenant, page, size,customerType?:false,ds,onClick,firmType,client)
    }

    @Get("/vendor/all/aggregated")
    fun getAggregatedDebitNoteData(@RequestParam("partnerIds") partnerIds: List<Long>?,
                                   @RequestParam(required = false) partnerId: Long?,
                                   @RequestParam(required = false) partnerDetailId: Long?,
                                   @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startAt: LocalDate?,
                                   @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endAt: LocalDate?,
                                   @RequestParam("noteType") noteType: NoteTypes?,
                                   @RequestParam(required = false) customerType: Boolean?,
                                   @RequestParam("tenant") tenant: String,
                                   @RequestParam(required = false) ds: String? = null,
                                   @RequestParam(required = false) firmType: List<Int>?,
                                   @RequestParam ("client") client: List<InvoiceType>): AggregatedDebitNoteDataDto {
        return debitNoteService.getAggregatedDebitNoteData(partnerIds, partnerId,partnerDetailId, startAt, endAt, noteType, tenant,customerType?:false,ds,firmType,client)
    }



    @RequestMapping("/url", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getDebitSummary(
            @RequestParam(required = true) tenant: String,
            @RequestParam(required = false) customerType: Boolean?,
            @RequestParam(required = true) createdBy: String,
            @RequestParam(required = false) ds: String? = null,
            @RequestParam(required = false) firmType: List<Int>?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startAt: LocalDate?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endAt: LocalDate?,
            @RequestParam("client") client: List<InvoiceType>): CreateResultData {
        return debitNoteService.getDebitNoteURL(tenant,createdBy,customerType?:false,ds,firmType,startAt,endAt,client)
    }


    @RequestMapping("/download", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getDebitSummaryDownload(@RequestParam(required = true) createdBy: String,
                                @RequestParam(required = false) customerType: Boolean?,
                                @RequestParam(required = true) tenant: String,
                                @RequestParam(required = false) ds: String? = null): CreateResultData {
        return debitNoteService.getDebitDownload(createdBy,tenant,customerType,ds)
    }


    @RequestMapping("/detail/url", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getDebitNoteDetailSummary(
            @RequestParam(required = true) supplierId: Long,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) from: LocalDate?,
            @RequestParam(required = false) customerType: Boolean?,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) to: LocalDate?,
            @RequestParam(required = true) tenant: String,
            @RequestParam(required = true) createdBy: String,
            @RequestParam(required = false) status: NoteStatus?,
            @RequestParam(required = false) ds: String? = null): CreateResultData {
        return debitNoteService.getDebitNoteDetailURL(supplierId,tenant,createdBy,from,to,status,customerType?:false,ds)
    }


    @RequestMapping("/detail/download", method = arrayOf(RequestMethod.GET), produces = arrayOf(MediaType.APPLICATION_JSON_VALUE))
    fun getDebitNoteDetailSummaryDownload(@RequestParam(required = true) createdBy: String,
                                          @RequestParam(required = true) tenant: String,
                                          @RequestParam(required = false) customerType: Boolean?,
                                          @RequestParam(required = false) ds: String? = null): CreateResultData {
        return debitNoteService.getDebitDetailDownload(createdBy,tenant,customerType?:false,ds)
    }

    @Get("/purchasereturns")
    fun getDebitNotePrsByDebitNoteNumber(@RequestParam("debitNoteNumber") debitNoteNumber: String,
                                         @RequestParam("tenant") tenant: String,
                                         @RequestParam(required = false) ds: String? = null) =
             debitNoteService.getDebitNotePrByDebitNoteNumber(debitNoteNumber, tenant,ds)

    @Post("/customer")
    fun retryCreateCustomerDebitNotes(@RequestBody saleReturnDebitNoteDto: SaleReturnDebitNoteDto): ResponseEntity<Any> {
        return ResponseEntity.ok(debitNoteService.createCustomerDebitNotes(saleReturnDebitNoteDto,NoteTypes.SR_ACCEPTED, false))
    }

    @Get("/{tenant}/purchaseReturnPDF/{returnOrderId}")
    fun getPurchaseReturnPDFs(@PathVariable("returnOrderId") returnOrderId: Long,
                              @PathVariable("tenant") tenant: String) = debitNoteService.prepareS3URLForDebitNotes(returnOrderId, tenant)

    @Get("/{tenant}/print/purchaseReturn")
    fun getPurchaseReturnPDFsByDnNumber(@PathVariable("tenant") tenant: String,
                                        @RequestParam("debitNoteNumber") debitNoteNumber: String) = debitNoteService.prepareS3URLForDebitNotesByDnNumber(debitNoteNumber, tenant)

    @Get("trigger/autocreate_credit_notes_based_on_fsc")
    fun createCnToDnFsc() = debitNoteService.createDntoCnWithFSC()

    @GetMapping("/getRioReturnItemData")
    fun getRioReturnItemData( @RequestParam packageId: String,
                               @RequestParam includeItems: Boolean=true,
                              @RequestParam ds: String? = null,
                              @RequestParam tenant: String): RioReturnItemsDto?{
        return debitNoteService.getRioReturnItemData(packageId,includeItems,ds?:tenant)
    }

    @PostMapping("create-rio-dn")
    fun createRioDebitNotes(@RequestBody rioReturnEvent: RIOReturnEvent){
        return debitNoteHandler.createRioDebitNotes(rioReturnEvent)
    }

    @PostMapping("create-wms-dn")
    fun createRioDebitNotes(@RequestBody rioReturnDebitNoteDto: RioReturnEventDataDto){
        return debitNoteHandler.createRioDebitNotes(rioReturnDebitNoteDto)
    }

    @PostMapping("/createAutoCn")
    fun createProofCnToCn(@RequestParam debitNoteId: Long){
        debitNoteService.createProofCnToCn(debitNoteId)
    }
}