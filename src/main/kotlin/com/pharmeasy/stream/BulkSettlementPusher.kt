package com.pharmeasy.stream

import com.pharmeasy.data.BulkSettlementFileValidationDto
import com.pharmeasy.data.ops.BulkSettlementFileProcess
import com.pharmeasy.model.ProcessSettlementTaskDto
import org.springframework.integration.annotation.Gateway
import org.springframework.integration.annotation.MessagingGateway

@MessagingGateway
interface BulkSettlementPusher {
    @Gateway(requestChannel = BulkSettlementSink.BULK_SETTLEMENT_FILE_VALIDATION_PRODUCER)
    fun bulkSettlementFileValidationProducer(bulkSettlementFileValidationDto: BulkSettlementFileValidationDto)

    @Gateway(requestChannel = BulkSettlementSink.BULK_SETTLEMENT_FILE_PROCESS_PRODUCER)
    fun bulkSettlementFileProcessProducer(bulkSettlementFileProcess: BulkSettlementFileProcess)
}