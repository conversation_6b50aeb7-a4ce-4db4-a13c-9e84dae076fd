package com.pharmeasy.stream

import com.pharmeasy.data.BulkSettlementFileValidationDto
import com.pharmeasy.data.ops.BulkSettlementFileProcess
import com.pharmeasy.model.ProcessSettlementTaskDto
import com.pharmeasy.service.SettlementFileValidator
import com.pharmeasy.service.SettlementTaskProcessor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.stream.annotation.StreamListener
import org.springframework.stereotype.Service

@Service
class BulkSettlementHandler {
    @Autowired
    private lateinit var settlementFileValidator: SettlementFileValidator
    @Autowired
    private lateinit var settlementTaskProcessor: SettlementTaskProcessor
    @StreamListener(BulkSettlementSink.BULK_SETTLEMENT_FILE_VALIDATION_CONSUMER)
    fun bulkSettlementFileValidationConsumer(bulkSettlementFileValidationDto: BulkSettlementFileValidationDto) {
        settlementFileValidator.validateFile(bulkSettlementFileValidationDto.key, bulkSettlementFileValidationDto.taskId, bulkSettlementFileValidationDto.ds)
    }

    @StreamListener(BulkSettlementSink.BULK_SETTLEMENT_FILE_PROCESS_CONSUMER)
    fun bulkSettlementFileProcessConsumer(bulkSettlementFileProcess: BulkSettlementFileProcess) {
        settlementTaskProcessor.processTask(bulkSettlementFileProcess.processSettlementTaskDto, bulkSettlementFileProcess.user, bulkSettlementFileProcess.ds, bulkSettlementFileProcess.tenant, bulkSettlementFileProcess.userHash)
    }
}