package com.pharmeasy.stream

import org.springframework.cloud.stream.annotation.Input
import org.springframework.cloud.stream.annotation.Output
import org.springframework.messaging.SubscribableChannel

interface BulkSettlementSink {
    companion object {
        const val BULK_SETTLEMENT_FILE_VALIDATION_PRODUCER = "bulkSettlementFileValidationProducer"
        const val BULK_SETTLEMENT_FILE_VALIDATION_CONSUMER = "bulkSettlementFileValidationConsumer"
        const val BULK_SETTLEMENT_FILE_PROCESS_PRODUCER = "bulkSettlementFileProcessProducer"
        const val BULK_SETTLEMENT_FILE_PROCESS_CONSUMER = "bulkSettlementFileProcessConsumer"
    }

    @Output
    fun bulkSettlementFileValidationProducer(): SubscribableChannel

    @Input
    fun bulkSettlementFileValidationConsumer(): SubscribableChannel

    @Output
    fun bulkSettlementFileProcessProducer(): SubscribableChannel

    @Input
    fun bulkSettlementFileProcessConsumer(): SubscribableChannel
}