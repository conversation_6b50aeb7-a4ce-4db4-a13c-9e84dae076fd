package com.pharmeasy.config

import com.pharmeasy.stream.*
import org.springframework.cloud.stream.annotation.EnableBinding
import org.springframework.context.annotation.Configuration
import org.springframework.integration.annotation.IntegrationComponentScan

@Configuration
@EnableBinding(value = [DebitNoteSink::class,
    BkInvoiceSink::class,
    CustomerDebitNoteSink::class,
    CustomerInvoiceSink::class,
    GatePassSink::class,
    B3InvoiceSink::class,
    B3DebitNoteSink::class,
    ConsolInvoiceSink::class,
    AutoSettlementSink::class,
    CreditControlSink::class,
    RioInvoiceSink::class,
    SettlementInvoiceListSink::class,
    EinvoiceCreationSink::class,
    OPSSink::class,
    DeliveryChallanSink::class,
    DcCAllBackEventSink::class,
    RioDebitNoteSink::class,
    InvoiceSettlementUpdateSink::class,
    InvoiceSettlementUpdateSink::class,
    BlockedVendorSettlementSink::class,
    MigrationSink::class,
    ChequeStatusDataSink::class,
    SendDraftFileSink::class,
    EwayBillCreationSink::class,
    PaperExcelSink::class,
    SlipEventSink::class,
    SendFileEventSink::class,
    EwayBillCreationSink::class,
    MakerCheckerSink::class,
    EInvoiceRetryListener::class,
    BulkSettlementSink::class,
    CronJobSink::class
    ]
)
@IntegrationComponentScan(basePackages = ["com.pharmeasy"])
class StreamConfig
