package com.pharmeasy.service

import com.pharmeasy.data.SettlementTask
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.CreateResultData
import com.pharmeasy.model.PaginationDto
import com.pharmeasy.model.ProcessSettlementTaskDto
import com.pharmeasy.model.SettlementFileMeta
import com.pharmeasy.model.SettlementTaskSummary
import com.pharmeasy.model.UrlDataDTO
import com.pharmeasy.repo.SettlementTaskRepo
import com.pharmeasy.repo.read.SettlementTaskReadRepo
import com.pharmeasy.stream.BulkSettlementPusher
import com.pharmeasy.type.PartnerType
import com.pharmeasy.type.PaymentType
import com.pharmeasy.type.SettlementFileStatus
import com.pharmeasy.type.SettlementTaskStatus
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import com.pharmeasy.data.BulkSettlementFileValidationDto
import com.pharmeasy.data.ops.BulkSettlementFileProcess

@Service
class SettlementTaskService(
    @Value("\${app.aws.s3.bucket}")
    private var bucket: String,
    @Value("\${app.aws.s3.prefix}")
    private var settlementPrefix: String,
    private var settlementTaskReadRepo: SettlementTaskReadRepo,
    private var s3FileUtilityService: S3FileUtilityService,
    private  var companyService: CompanyService,
    private  var s3Uploader: S3Uploader,
    private  var settlementTaskRepo: SettlementTaskRepo,
    private  var bulkSettlementPusher: BulkSettlementPusher

) {

    companion object {
        private val log = LoggerFactory.getLogger(SettlementTaskService::class.java)
    }

    fun getSettlementTaskSummary(taskId: Long): SettlementTaskSummary {
        return settlementTaskReadRepo.getSettlementTaskSummary(taskId)
    }

    fun getSettlementTaskItems(taskId: Long, page: Int?, size: Int?, invoiceNo: String?): PaginationDto {
        if(invoiceNo != null) {
            val item = settlementTaskReadRepo.filterByInvoiceNumber(taskId, invoiceNo)
            return PaginationDto(1, 1, hasPrevious = false, hasNext = false, data = listOf(item))
        }
        val pageable = PageRequest.of(page ?: 0, size ?: 10)
        val items = settlementTaskReadRepo.getSettlementTaskItemDetail(taskId, invoiceNo, pageable)
        return PaginationDto(items.totalElements, items.totalPages, items.hasPrevious(), items.hasNext(), items.content)
    }


    fun downloadSettlementTaskItems(taskId: Long): CreateResultData {
        val items = settlementTaskReadRepo.getSettlementTaskItemDetailByTaskId(taskId)
        val csvPrinter: CSVPrinter
        val bytes = ByteArrayOutputStream()
        csvPrinter = CSVPrinter(
            bytes.bufferedWriter(), CSVFormat.DEFAULT
                .withHeader("Partner Detail Id", "Invoice Number", "Amount", "Settled")
        )
        items.forEach {
            csvPrinter.printRecord(it.partnerDetailId, it.invoiceNumber, it.amount, it.settled)
        }

        csvPrinter.flush()

        val url = s3FileUtilityService.uploadFile(bytes.toByteArray(), "CSV", "SettlementTask_${taskId}")
        return CreateResultData(200, "SUCCESS", url)
    }

    fun getFileUploadUrl(mode: PaymentType): UrlDataDTO{
        val filename = "$settlementPrefix/${LocalDate.now().format(DateTimeFormatter.ISO_DATE)}/${mode}_${System.currentTimeMillis()}.csv"
        return UrlDataDTO(s3Uploader.preSignedURL(bucket,filename), filename)
    }

    fun uploadSettlementFile(settlementFileMeta: SettlementFileMeta, tenant: String,
                             user: String, ds: String? = null,
                             customerType: Boolean = false): PaginationDto {
        log.debug(
            "Settlement file received. Filename: {}, user: {}, mode: {}, partnerLevel :{} ",
            settlementFileMeta.objectKey,
            user,
            settlementFileMeta.mode,
            settlementFileMeta.isPartnerLevel
        )
        val company = companyService.getCompanyTenantMappingObject(ds?:tenant)
            ?: throw RequestException("No company found for tenant $tenant")

        val settlementTask = SettlementTask(company = company, createdBy = user,
            fileStatus = SettlementFileStatus.VALIDATION_IN_PROGRESS,
            partnerType = if(customerType) PartnerType.CUSTOMER else PartnerType.VENDOR,
            settlementMode = settlementFileMeta.mode, taskStatus = SettlementTaskStatus.PENDING, s3Uri = settlementFileMeta.objectKey, isPartnerLevel = settlementFileMeta.isPartnerLevel)

        val savedSettlementTask = settlementTaskRepo.save(settlementTask)

        bulkSettlementPusher.bulkSettlementFileValidationProducer(BulkSettlementFileValidationDto(settlementFileMeta.objectKey, savedSettlementTask.id!!,ds))


        return getTasks(tenant = tenant)
    }

    fun processSettlementTask(processTaskDto: ProcessSettlementTaskDto, tenant: String,
                              user: String, ds: String? = null, userHash: String): PaginationDto {
        val company = companyService.getCompanyTenantMappingObject(ds?:tenant)
            ?: throw RequestException("No company found for tenant $tenant")

        val task = settlementTaskReadRepo.findInProgressTaskInCompany(company.id)
        if(task.isEmpty()) {
            val taskToExec = settlementTaskRepo.findById(processTaskDto.taskId)
            if (!taskToExec.isPresent) throw RequestException("No settlement task found with id $processTaskDto.taskId")

            val actualTask = taskToExec.get()
            actualTask.taskStatus = SettlementTaskStatus.IN_PROGRESS
            actualTask.verifiedBy = user
            actualTask.verifiedOn = LocalDateTime.now()
            settlementTaskRepo.save(actualTask)
            bulkSettlementPusher.bulkSettlementFileProcessProducer(
                BulkSettlementFileProcess(
                    processTaskDto,
                    user,
                    ds,
                    tenant,
                    userHash
                )
            )

            return getTasks(tenant = tenant)
        } else {
            val msg = "A Settlement task is already in progress. Please wait for it's finish"
            log.warn(msg)
            throw RequestException(msg)
        }
    }

    fun cancelSettlementTask(taskId: Long, tenant: String): PaginationDto {
        settlementTaskRepo.cancelTask(taskId)
        return getTasks(tenant = tenant)
    }

    fun getTasks(fromDateTime: LocalDateTime? = null, toDateTime: LocalDateTime? = null,
                         mode: PaymentType? = null, status: SettlementTaskStatus? = null,
                         customerType: Boolean = false, ds: String? = null, tenant: String,
                         page: Int? = null, size: Int? = null,isPartnerLevel: Boolean? = null): PaginationDto {
        val partnerType = if(customerType) PartnerType.CUSTOMER else PartnerType.VENDOR
        val company = companyService.getCompanyTenantMappingObject(ds?:tenant) ?: throw RequestException("No company mapping found for tenant $tenant")
        val pageable = PageRequest.of( page ?: 0,  size ?: 10, Sort.by(Sort.Direction.DESC, "id"))
        val settlementTasks = settlementTaskReadRepo.findAllTasks(fromDateTime, toDateTime, mode, status, partnerType, company.id,isPartnerLevel, pageable)
        return PaginationDto(settlementTasks.totalElements, settlementTasks.totalPages, settlementTasks.hasPrevious(),
            settlementTasks.hasNext(), settlementTasks.content)
    }
}