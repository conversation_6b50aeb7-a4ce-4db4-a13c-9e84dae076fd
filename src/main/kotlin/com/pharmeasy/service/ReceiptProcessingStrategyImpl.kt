package com.pharmeasy.service

import com.pharmeasy.data.DraftReceipt
import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.service.abstracts.BaseReceiptProcessingStrategy
import com.pharmeasy.service.abstracts.SettleableProcessorFactory
import com.pharmeasy.service.strategy.PaymentProcessorStrategyFactory
import com.pharmeasy.type.AdvancePaymentSource
import com.pharmeasy.type.ReceiptStatus
import org.springframework.stereotype.Service

@Service
class ReceiptProcessingStrategyImpl(
    private val companyService: CompanyService,
    private val documentService: DocumentMasterService,
    private val paymentProcessorStrategyFactory: PaymentProcessorStrategyFactory,
    private val settleableProcessorFactory: SettleableProcessorFactory,
    private val receiptService: ReceiptServiceV2
) : BaseReceiptProcessingStrategy(
        companyService,
        documentService,
        paymentProcessorStrategyFactory,
        settleableProcessorFactory
    ) {
    override val source: AdvancePaymentSource = AdvancePaymentSource.RIO_COLLECTIONS

    fun createReceipt(draftReceipt: DraftReceipt): Receipt {
        require(draftReceipt.status == ReceiptStatus.APPROVED) { "Receipt status must be APPROVED" }
        val existingReceipt = receiptService.getReceiptByDraftId(draftReceipt.id!!)
        val receipt =
            if (existingReceipt == null) {
                val receipt = Receipt(draftReceipt = draftReceipt, "SYSTEM") // User gets updated for WEB flows
                receiptService.saveReceipt(receipt)
            } else {
                existingReceipt
            }
        addSettlements(receipt, draftReceipt.settleableMappings)
        return receipt
    }

    fun addSettlements(
        receipt: Receipt,
        settlementMapping: List<DraftReceiptEntityMapping>
    ) {
        settlementMapping.forEach {
            receiptService.addSettlement(receipt, it)
        }
    }
}
