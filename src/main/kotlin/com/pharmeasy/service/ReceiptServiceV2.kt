package com.pharmeasy.service

import com.pharmeasy.data.DraftReceiptEntityMapping
import com.pharmeasy.data.Receipt
import com.pharmeasy.data.ReceiptSettlement
import com.pharmeasy.data.ReceiptSettlementId
import com.pharmeasy.repo.ReceiptRepo
import com.pharmeasy.repo.ReceiptSettlementRepo
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class ReceiptServiceV2(
    private val receiptRepo: ReceiptRepo,
    private val settlementService: SettlementService,
    private val receiptSettlementRepo: ReceiptSettlementRepo
) {
    fun getReceiptByDraftId(draftId: Long): Receipt? {
        return receiptRepo.getReceiptByDraftId(draftId)
    }

    fun saveReceipt(receipt: Receipt): Receipt {
        return receiptRepo.save(receipt)
    }

    fun addSettlement(
        receipt: Receipt,
        payment: DraftReceiptEntityMapping
    ) {
        val settlement = settlementService.createSettlementFromReceipt(receipt, payment)
        receiptSettlementRepo.save(
            ReceiptSettlement(
                ReceiptSettlementId(receipt.id!!, settlement.id),
                LocalDateTime.now(),
                receipt.updatedBy!!
            )
        )
    }
}
