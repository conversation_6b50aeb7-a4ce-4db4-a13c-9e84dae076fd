package com.pharmeasy.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.pharmeasy.data.*
import com.pharmeasy.exception.RequestException
import com.pharmeasy.model.SettlementFileDto
import com.pharmeasy.model.SettlementUniqueKey
import com.pharmeasy.repo.*
import com.pharmeasy.repo.read.BkInvoiceReadRepo
import com.pharmeasy.type.*
import com.pharmeasy.util.SettlementTaskUtils
import com.pharmeasy.util.SettlementTaskUtils.Companion.isValidAmount
import com.pharmeasy.util.SettlementTaskUtils.Companion.isValidDate
import com.pharmeasy.util.SettlementTaskUtils.Companion.isValidPresentDate
import com.pharmeasy.util.SettlementTaskUtils.Companion.isValidNumber
import com.pharmeasy.util.SettlementTaskUtils.Companion.isValidString
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVPrinter
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.io.InputStreamReader
import java.lang.Exception
import java.lang.StringBuilder
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import javax.annotation.PostConstruct
import kotlin.math.abs

@Component
class SettlementFileValidator(
    @Value("\${app.aws.s3.prefix}")
    private var settlementPrefix: String,
    private var settlementTaskRepo: SettlementTaskRepo,
    private var bankRepo: BkBankRepo,
    private var s3Uploader: S3Uploader,
    private var banks: List<BkBank>,
    private var invoiceRepo: BkInvoiceReadRepo,
    private var settlementTaskItemRepo: SettlementTaskItemRepo
) {

    companion object {
        private val log = LoggerFactory.getLogger(SettlementFileValidator::class.java)
    }

    @PostConstruct
    fun populateBanks() {
        banks = bankRepo.findAll()
    }

    @Transactional
    fun validateFile(objectKey: String, settlementTaskId: Long,ds: String? = null)  {
        val settlementTask = settlementTaskRepo.findById(settlementTaskId).orElseThrow { RequestException("Settlement task not found") }
        val inputStream = s3Uploader.read(objectKey)
        log.debug("Bulk settlement file by mode ${settlementTask.settlementMode} uploaded by ${settlementTask.createdBy}" +
                    ". Task id: ${settlementTask.id}")
        val csvParser = CSVFormat.DEFAULT.withFirstRecordAsHeader().parse(InputStreamReader(inputStream))
        when(settlementTask.settlementMode) {
            PaymentType.CASH, PaymentType.NEFT -> cashOrNeftValidator(csvParser, settlementTask)
            PaymentType.CHEQUE -> chequeValidator(csvParser, settlementTask,ds)
            else -> log.debug("Bulk settlement not supported for mode ${settlementTask.settlementMode}")
        }
    }

    private fun chequeValidator(csvParser: CSVParser, settlementTask: SettlementTask,ds: String? = null) {
        try {
            log.debug("Settlement file validation started. Task id: ${settlementTask.id}, mode: ${settlementTask.settlementMode}")
            var isValid: Boolean
            var sb: StringBuilder
            var item: SettlementFileDto
            var invoiceNumToItemMap = mutableMapOf<String?, MutableList<SettlementFileDto>>()
            val partnerIdToItemMap = mutableMapOf<String?, MutableList<SettlementFileDto>>()
            val uniqueKeyToItemMap = mutableMapOf<SettlementUniqueKey, SettlementFileDto>()
            var noOfRecords = 0
            var checkPartnerLevelDetail = true

            // Parse all records and store in map
            csvParser.records.forEach { record ->
                sb = StringBuilder()
                noOfRecords++
                item = SettlementFileDto()
                isValid = true
                item.partnerDetailId = record.get(SettlementTaskUtils.chequeSettlementFileCols[0])
                if (!isValidNumber(item.partnerDetailId)) {
                    sb.append("Invalid partner detail id ")
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.invoiceNo = if(settlementTask.isPartnerLevel) null else record.get(SettlementTaskUtils.chequeSettlementFileCols[1])
                if(!settlementTask.isPartnerLevel)
                if (!isValidString(item.invoiceNo)) {
                    sb.append("Invalid invoice ")
                    isValid = false
                }

                item.amount = if(settlementTask.isPartnerLevel) record.get(SettlementTaskUtils.chequeSettlementFileCols[7]) else record.get(SettlementTaskUtils.chequeSettlementFileCols[2])
                if (!isValidAmount(item.amount)) {
                    sb.append("Invalid amount ")
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.chequeOrRefNumber = record.get(SettlementTaskUtils.chequeSettlementFileCols[3])
                if (!isValidString(item.chequeOrRefNumber)) {
                    sb.append("Invalid cheque no")
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.chequeDate = record.get(SettlementTaskUtils.chequeSettlementFileCols[4])
                if(!isValidDate(item.chequeDate)) {
                    sb.append("Invalid date format")
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.bankName = record.get(SettlementTaskUtils.chequeSettlementFileCols[5])
                log.debug("bank name passed : ${item.bankName}")
                if (this.findBank(item.bankName) == null) {
                    sb.append("Invalid bank. Possible values " +
                            this.banks.map { it.name }.joinToString(prefix = "<", postfix = ">", separator = ",")
                    )
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.paymentDate = record.get(SettlementTaskUtils.chequeSettlementFileCols[6])
                if (!isValidPresentDate(item.paymentDate)) {
                    sb.append("Future payment date or invalid date format. Accepted dd/MM/yyyy")
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.isValidRow = isValid

                if(!settlementTask.isPartnerLevel) {
                    mapInvoices(invoiceNumToItemMap, item)
                }else{
                    if (!partnerIdToItemMap.containsKey(item.partnerDetailId)) {
                        partnerIdToItemMap[item.partnerDetailId] = mutableListOf()
                    }
                    partnerIdToItemMap[item.partnerDetailId]?.add(item)
                }

                if(item.isValidRow) {
                    val uniqueKey = SettlementUniqueKey(item.partnerDetailId?.toLong()!!, item.chequeOrRefNumber!!,
                        LocalDate.parse(item.paymentDate, DateTimeFormatter.ofPattern("dd/MM/yyyy")), bank = null)
                    if(uniqueKeyToItemMap[uniqueKey] == null) {
                        uniqueKeyToItemMap[uniqueKey] = item
                    } else {
                        val bankNameFirstSeen = uniqueKeyToItemMap[uniqueKey]?.bankName!!
                        val chequeDateFirstSeen = LocalDate.parse(uniqueKeyToItemMap[uniqueKey]?.chequeDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))!!
                        if(!bankNameFirstSeen.equals(item.bankName, ignoreCase = true)) {
                            sb.append("Bank name inconsistent across same (payment reference number,payment date,partner id)")
                        }
                        if(!chequeDateFirstSeen.isEqual(LocalDate.parse(item.chequeDate, DateTimeFormatter.ofPattern("dd/MM/yyyy")))) {
                            sb.append("Cheque date inconsistent across same (payment reference number,payment date,partner id)")
                        }
                    }
                }
                item.failureReason = sb.toString()
            }

            if(noOfRecords == 0) {
                this.markTaskFailureForEmptyFile(settlementTask, SettlementTaskUtils.chequeSettlementFileCols.toMutableList())
                return
            }
            var settlement = mutableListOf<SettlementFileDto>()
            validateInvoices(settlementTask, checkPartnerLevelDetail, partnerIdToItemMap, settlement, invoiceNumToItemMap)

            val headers = SettlementTaskUtils.chequeSettlementFileCols.toMutableList()
            headers.add("Valid")
            headers.add("Failure Reason")

            val bytes = ByteArrayOutputStream()
            val csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT.withHeader(*headers.toTypedArray()))
            var isValidFile = true

            invoiceNumToItemMap.forEach { (_, invoices) ->
                invoices.forEach { record ->
                    isValidFile = isValidFile && record.isValidRow
                    csvPrinter.printRecord(
                        record.partnerDetailId, record.invoiceNo, record.amount, record.chequeOrRefNumber,
                        record.chequeDate, record.bankName, record.paymentDate, record.amount, record.isValidRow, record.failureReason
                    )
                }
            }

            csvPrinter.flush()

            log.debug("Settlement file validation completed. Valid = $isValidFile. Task id: ${settlementTask.id}, mode = ${settlementTask.settlementMode}")

            val filename = "$settlementPrefix/${LocalDate.now().format(DateTimeFormatter.ISO_DATE)}/" +
                    "${settlementTask.settlementMode}_${isValidFile}_${System.currentTimeMillis()}.csv"
            val uri = s3Uploader.secureUploadWithKey(bytes.toByteArray(), FileType.CSV, 1000 * 60 * 60 * 24 * 7, filename)
            settlementTask.s3Uri = uri!!
            if (!isValidFile) {
                settlementTask.fileStatus = SettlementFileStatus.VALIDATION_FAILED
                settlementTask.taskStatus = SettlementTaskStatus.FAILED
            } else {
                val settlementTaskItems = createSettlementTaskItems(invoiceNumToItemMap, settlementTask)
                val subList = settlementTaskItems.windowed(500, 500, partialWindows = true)
                subList.forEach { settlementTaskItemRepo.saveAll(it) }
                settlementTask.fileStatus = SettlementFileStatus.VALIDATION_PASSED
                settlementTask.taskStatus = SettlementTaskStatus.PENDING
            }
            settlementTaskRepo.save(settlementTask)
        } catch (e:Exception) {
            log.error("File validation failed for task id ${settlementTask.id}, mode: ${settlementTask.settlementMode}",e)
            settlementTask.fileStatus = SettlementFileStatus.TECHNICAL_FAILURE
            settlementTask.taskStatus = SettlementTaskStatus.FAILED
            settlementTaskRepo.save(settlementTask)
        }
    }

    private fun cashOrNeftValidator(csvParser: CSVParser, settlementTask: SettlementTask) {
        try {
            log.debug("Settlement file validation started. Task id: ${settlementTask.id}, mode: ${settlementTask.settlementMode}")
            var isValid: Boolean
            var sb: StringBuilder
            var item: SettlementFileDto
            val invoiceNumToItemMap = mutableMapOf<String?, MutableList<SettlementFileDto>>()
            val partnerIdToItemMap = mutableMapOf<String?, MutableList<SettlementFileDto>>()
            var noOfRecords = 0
            var checkPartnerLevelDetail = true

            // Parse all records and store in map
            csvParser.records.forEach { record ->
                sb = StringBuilder()
                noOfRecords++
                item = SettlementFileDto()
                isValid = true
                item.partnerDetailId = record.get(SettlementTaskUtils.neftOrCashSettlementFileCols[0])
                if (!isValidNumber(item.partnerDetailId)) {
                    sb.append("Invalid partner detail id ")
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.invoiceNo = if(settlementTask.isPartnerLevel) null else record.get(SettlementTaskUtils.neftOrCashSettlementFileCols[1])
                if(!settlementTask.isPartnerLevel)
                if (!isValidString(item.invoiceNo)) {
                    sb.append("Invalid invoice ")
                    isValid = false
                }

                item.amount = if(settlementTask.isPartnerLevel) record.get(SettlementTaskUtils.neftOrCashSettlementFileCols[5]) else record.get(SettlementTaskUtils.neftOrCashSettlementFileCols[2])
                if (!isValidAmount(item.amount)) {
                    sb.append("Invalid amount ")
                    isValid = false
                    checkPartnerLevelDetail = false
                }

                item.chequeOrRefNumber = record.get(SettlementTaskUtils.neftOrCashSettlementFileCols[3])
                if (!isValidString(item.chequeOrRefNumber)) {
                    sb.append("Invalid payment ref no")
                    isValid = false
                }

                item.paymentDate = record.get(SettlementTaskUtils.neftOrCashSettlementFileCols[4])
                if (!isValidPresentDate(item.paymentDate)) {
                    sb.append("Future payment date or invalid date format. Accepted dd/MM/yyyy")
                    isValid = false
                    checkPartnerLevelDetail = false
                }
                item.failureReason = sb.toString()
                item.isValidRow = isValid

                if(!settlementTask.isPartnerLevel) {
                        mapInvoices(invoiceNumToItemMap, item)
                }else{
                    if (!partnerIdToItemMap.containsKey(item.partnerDetailId)) {
                        partnerIdToItemMap[item.partnerDetailId] = mutableListOf()
                    }
                    partnerIdToItemMap[item.partnerDetailId]?.add(item)
                }
            }

            if(noOfRecords == 0) {
                this.markTaskFailureForEmptyFile(settlementTask, SettlementTaskUtils.neftOrCashSettlementFileCols.toMutableList())
                return
            }
            var settlement = mutableListOf<SettlementFileDto>()
            validateInvoices(settlementTask, checkPartnerLevelDetail, partnerIdToItemMap, settlement, invoiceNumToItemMap)
            val headers = SettlementTaskUtils.neftOrCashSettlementFileCols.toMutableList()
            headers.add("Valid")
            headers.add("Failure Reason")

            val bytes = ByteArrayOutputStream()
            val csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT.withHeader(*headers.toTypedArray()))
            var isValidFile = true

            invoiceNumToItemMap.forEach { (_, invoices) ->
                invoices.forEach { record ->
                    isValidFile = isValidFile && record.isValidRow
                    csvPrinter.printRecord(
                        record.partnerDetailId, record.invoiceNo, record.amount, record.chequeOrRefNumber,
                        record.paymentDate, record.isValidRow, record.failureReason
                    )
                }
            }

            csvPrinter.flush()

            log.debug("File validation completed. Valid = $isValidFile. Task id: ${settlementTask.id}, mode = ${settlementTask.settlementMode}")

            val filename = "$settlementPrefix/${LocalDate.now().format(DateTimeFormatter.ISO_DATE)}/" +
                    "${settlementTask.settlementMode}_${isValidFile}_${System.currentTimeMillis()}.csv"
            val uri = s3Uploader.secureUploadWithKey(bytes.toByteArray(), FileType.CSV, 1000 * 60 * 60 * 24 * 7, filename)
            settlementTask.s3Uri = uri!!
            if (!isValidFile) {
                settlementTask.fileStatus = SettlementFileStatus.VALIDATION_FAILED
                settlementTask.taskStatus = SettlementTaskStatus.FAILED
            } else {
                val settlementTaskItems = createSettlementTaskItems(invoiceNumToItemMap, settlementTask)
                val subList = settlementTaskItems.windowed(500, 500, partialWindows = true)
                subList.forEach { settlementTaskItemRepo.saveAll(it) }
                settlementTask.fileStatus = SettlementFileStatus.VALIDATION_PASSED
                settlementTask.taskStatus = SettlementTaskStatus.PENDING
            }
            settlementTaskRepo.save(settlementTask)
        } catch (e: Exception) {
            log.error("File validation failed for task id ${settlementTask.id}, mode: ${settlementTask.settlementMode}",e)
            settlementTask.fileStatus = SettlementFileStatus.TECHNICAL_FAILURE
            settlementTask.taskStatus = SettlementTaskStatus.FAILED
            settlementTaskRepo.save(settlementTask)
        }
    }

    private fun validateInvoices(settlementTask: SettlementTask, checkPartnerLevelDetail: Boolean, partnerIdToItemMap: MutableMap<String?, MutableList<SettlementFileDto>>, settlement: MutableList<SettlementFileDto>, invoiceNumToItemMap: MutableMap<String?, MutableList<SettlementFileDto>>) {
        if (settlementTask.isPartnerLevel) {
            if (checkPartnerLevelDetail) {
                this.validatePartnerDetailIdAndAmt(partnerIdToItemMap, settlementTask, settlement)
                settlement.forEach { fileObj ->

                    mapInvoices(invoiceNumToItemMap, fileObj)
                }
            } else {
                partnerIdToItemMap.forEach { (_, invoices) ->
                    invoices.forEach { fileObj ->

                        mapInvoices(invoiceNumToItemMap, fileObj)

                    }

                }
            }
        } else {
            this.validatePartnerDetailIdAndInvoiceNum(invoiceNumToItemMap, settlementTask)
        }
    }

    private fun mapInvoices(invoiceNumToItemMap: MutableMap<String?, MutableList<SettlementFileDto>>, fileObj: SettlementFileDto) {
        if (!invoiceNumToItemMap.containsKey(fileObj.invoiceNo)) {
            invoiceNumToItemMap[fileObj.invoiceNo] = mutableListOf()
        }
        invoiceNumToItemMap[fileObj.invoiceNo]?.add(fileObj)
    }

    private fun findBank(bank: String?): BkBank? {
        log.debug("bank names from table : ${this.banks} :: ${this.banks.find { it.name.equals(bank, ignoreCase = true) }}")
        return this.banks.find { it.name.equals(bank, ignoreCase = true) }
    }

    private fun validatePartnerDetailIdAndInvoiceNum(invoiceNumToItemMap: MutableMap<String?, MutableList<SettlementFileDto>>,
        settlementTask: SettlementTask) {
        val invoicesList = invoiceNumToItemMap.keys.toList()
        val invoiceSublist = invoicesList.windowed(500, 500, partialWindows = true)

        invoiceSublist.forEach { list ->

            val invoicesFromDb = invoiceRepo.getInvoicesInList(list, listOf(settlementTask.company.tenant), settlementTask.partnerType).associateBy { it.invoiceNum }

            // validate invoice number and partner detail id wrt Vault DB
            list.forEach { invoiceNum ->
                if(!invoicesFromDb.containsKey(invoiceNum)) { // if invoice not present in db
                    invoiceNumToItemMap[invoiceNum]?.forEach {
                        it.failureReason += " Invoice not present in vault"
                        it.isValidRow = false
                    }
                } else { // if present in db then match partner detail id
                    val invoiceFromDb = invoicesFromDb[invoiceNum]
                    invoiceNumToItemMap[invoiceNum]?.forEach {item ->
                        if(item.isValidRow && item.partnerDetailId?.toLong() != invoiceFromDb?.partnerDetailId) {
                            item.failureReason += " Invalid partner detail id for invoice"
                            item.isValidRow = false
                        }
                    }
                }
            }
        }
    }

    private fun validatePartnerDetailIdAndAmt(partnerIdToItemMap: MutableMap<String?, MutableList<SettlementFileDto>>,
                                                     settlementTask: SettlementTask,settlementFileDto: MutableList<SettlementFileDto>) {

        log.debug("inside PDI amt validator")
        partnerIdToItemMap.forEach{ pdi ->
            log.debug("for pdi : $pdi")
            var settlementDto = settlementFileDto
           var totalAmt: Double = pdi.value.map { it.amount!!.toDouble() }.sumByDouble {it}
           var totalInvAmt = invoiceRepo.getAllPartnerInvoiceOverDueAmount(settlementTask.partnerType, mutableListOf(settlementTask.company.tenant),pdi.key!!.toLong())?:0.0
            log.debug("totalAmt : $totalAmt and totalInvAmt : $totalInvAmt")
            if (totalAmt>totalInvAmt){
                pdi.value.forEach {
                    it.failureReason += " Total amount for given partner is more then total open invoice amount."
                    it.isValidRow = false
                    settlementDto.add(it)

                }
            }else {

                var page = 0
                var size = 50
                    var pagination = PageRequest.of(page, size)
                    var pendingInvoice = invoiceRepo.getVendorInvoiceData(settlementTask.partnerType, mutableListOf(settlementTask.company.tenant), pdi.key!!.toLong(), pagination)
                        var index = 0
                        var invAmt = pendingInvoice.content[index].totalPendingInvoiceAmount //20
                        log.debug("size pdi values : ${pdi.value.size}")
                        pdi.value.forEach { i ->
                            log.debug("pid obj : ${ObjectMapper().writeValueAsString(i)}")
                            var pdiAmt = i.amount!!.toBigDecimal() //100
                            do {
                                log.debug("current pdiAmt : $pdiAmt  , invAmt : $invAmt and total size ${pendingInvoice.content.size} and index : $index")
                                if(index < pendingInvoice.content.size) {
                                    var settlementFileObj = SettlementFileDto()
                                    if (pdiAmt >= invAmt) {
                                        settlementFileObj.partnerDetailId = pdi.key!!
                                        settlementFileObj.chequeDate = i.chequeDate
                                        settlementFileObj.paymentDate = i.paymentDate
                                        settlementFileObj.bankName = i.bankName
                                        settlementFileObj.amount = invAmt.toString()
                                        settlementFileObj.invoiceNo = pendingInvoice.content[index].invoiceNo
                                        settlementFileObj.isValidRow = true
                                        settlementFileObj.chequeOrRefNumber = i.chequeOrRefNumber

                                        settlementDto.add(settlementFileObj)
                                        index++
                                        pdiAmt -= invAmt
                                        if(index < pendingInvoice.content.size)
                                        invAmt = pendingInvoice.content[index].totalPendingInvoiceAmount
                                        log.debug("if : pid amt : ${pdiAmt} , invAmt : $invAmt , index : $index")
                                    } else {
                                        log.debug("else pid : $pdiAmt :${pdiAmt.setScale(2, BigDecimal.ROUND_UP)}")

                                        if(pdiAmt.setScale(2, BigDecimal.ROUND_UP) > BigDecimal.ZERO) {
                                        settlementFileObj.partnerDetailId = pdi.key!!
                                        settlementFileObj.chequeDate = i.chequeDate
                                        settlementFileObj.paymentDate = i.paymentDate
                                        settlementFileObj.bankName = i.bankName
                                            settlementFileObj.amount = pdiAmt.setScale(2, BigDecimal.ROUND_UP).toString()
                                        settlementFileObj.invoiceNo = pendingInvoice.content[index].invoiceNo
                                        settlementFileObj.isValidRow = true
                                        settlementFileObj.chequeOrRefNumber = i.chequeOrRefNumber
                                        settlementDto.add(settlementFileObj)
                                        invAmt -= pdiAmt
                                        pdiAmt = BigDecimal.ZERO
                                        log.debug("else : pid amt : ${pdiAmt} , invAmt : $invAmt , index : $index")
                                    }
                                    }
                                }else {
                                    if (pendingInvoice.hasNext()) {
                                        page++
                                        log.debug("final else : has next : ${pendingInvoice.hasNext()} , page : $page , index : $index")
                                        pendingInvoice = invoiceRepo.getVendorInvoiceData(settlementTask.partnerType, mutableListOf(settlementTask.company.tenant), pdi.key!!.toLong(), PageRequest.of(page, size))
                                        index = 0
                                        invAmt = pendingInvoice.content[index].totalPendingInvoiceAmount
                                        log.debug("final else : pid amt : ${pdiAmt} , invAmt : $invAmt , index : $index , prv - ${pendingInvoice.hasPrevious()} , next - ${pendingInvoice.hasNext()}, inv id - ${pendingInvoice.content[index].invoiceNo} ")

                                    }
                                }
                            }while (pdiAmt > BigDecimal(0.001))

                        }
            }

        }
    }



    private fun createSettlementTaskItems(invoiceNumToItemMap: MutableMap<String?, MutableList<SettlementFileDto>>,
                                          settlementTask: SettlementTask): List<SettlementTaskItem> {
        log.debug("Creating settlement task items for taskId: ${settlementTask.id}, mode: ${settlementTask.settlementMode}")
        val taskItems = mutableListOf<SettlementTaskItem>()
        invoiceNumToItemMap.forEach { (_, fileDtoList) ->
            fileDtoList.forEach { fileDto ->
                val taskItem = SettlementTaskItem(settlementTask = settlementTask, amount = fileDto.amount!!.toDouble(),
                    partnerDetailId = fileDto.partnerDetailId!!.toLong(), invoiceNumber = fileDto.invoiceNo!!,
                    chequeOrRefNo = fileDto.chequeOrRefNumber!!, bank = this.findBank(fileDto.bankName),
                    paymentDate = LocalDate.parse(fileDto.paymentDate, DateTimeFormatter.ofPattern("dd/MM/yyyy")),
                    chequeDate = if(fileDto.chequeDate == null) null else LocalDate.parse(fileDto.chequeDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                )
                taskItems.add(taskItem)
            }
        }
        log.debug("Total task items = ${taskItems.size} for taskId: ${settlementTask.id}, mode: ${settlementTask.settlementMode}")
        return taskItems
    }

    private fun markTaskFailureForEmptyFile(settlementTask: SettlementTask, fileHeader: MutableList<String>) {
        log.debug("Empty File for mode ${settlementTask.settlementMode} received. Validation failure")
        val bytes = ByteArrayOutputStream()
        val csvPrinter = CSVPrinter(bytes.bufferedWriter(), CSVFormat.DEFAULT)
        csvPrinter.printRecord(*fileHeader.toTypedArray())
        csvPrinter.flush()
        settlementTask.s3Uri = s3Uploader.secureUploadWithKey(bytes.toByteArray(), FileType.CSV, 1000 * 60 * 60 * 24 * 7, settlementTask.s3Uri)!!
        settlementTask.taskStatus = SettlementTaskStatus.FAILED
        settlementTask.fileStatus = SettlementFileStatus.VALIDATION_FAILED
        settlementTaskRepo.save(settlementTask)
    }
}