#Change this config file to correct config file
app:
  aws:
    region: ap-south-1
    s3:
      bucket: gomercury-vault-data
      prefix: settlement
      sdcn:
        folderPrefix: sdcn/
        allPartnerFilePrefix: partners/generated/
        uploadedPartnerFilePrefix: partners/uploaded/
        draftCnPrefix: draftCn/
        deliveryChallanPrefix: deliveryChallan/
  vault_file_mail_id:
    username: <EMAIL>
  vault_alert_mail:
    username: <EMAIL>
  vault_report_mail:
    username: <EMAIL>
  reportEmailEnabled: false
  alertEmailEnabled: false
  outstanding_round_off_value: 1
  url: http://dev.vault.gomercury.in/advance-payment
  vault:
   token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.247QywUJV6p02cnDrFv32kEQeW8VWBgsqIo69mI9V3N5PReMRUL3LRdWjlz0ErF6Z3VmgVnx-pfzyb5Y0DMN9Q
   template:
     ledger_template: vault_ledger_template_v1
  dnToRetailerCBTemplate: debit_note_retailer_v1
  dnToRetailerCBHsn: 999794
  dnToRetailerCBTax: 18
  product:
    url: https://stage-catalog-api-gateway.dev.pharmeasy.in
  partner:
    retailer: 3
    retailerInClinic: 13
    retailerFOFO: 12
    retailerCOCO: 17
    retailerHospitals: 15
    hospitals: 19
    retailerPL: 14
    retailerAlpha: 8
    cnf: 1
    distributor: 2
    holding: 4
    manufacturer: 5
    retailerMarketPlace: 9
  notificationservice:
    userId: pe_user
    event: CUSTOMER_LEDGER_REPORT_NEW1
    tenant: PE_PHARMA
    source: ABACUS_SERVICE
    url: https://notifications-requests.dev.pharmeasy.in
    fromMailId: <EMAIL>
    internalMailId: <EMAIL>
  retail-io:
    url: https://dev.retailio.in/api/oneroof
    version: 1.0.0
    source: vault-client
    key: n9d8342b-2b52-4c80-a987-360e19fffc74

  taxPercentage:
    slabDiscount: 12
    offlinePromotion: 18
    otcMarginLeakage: 18
    salesIncentive: 18

  maxCashLimit: 199999

  rio-invoice:
    disabled-pdi: 2816,6059

  mercury:
    token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.x9TV3x8_rQmS_qilFTcTmJiLl_QJghogrYLZUr75TGWLgDOV-RR3tPy3mMeUDm5T4sEVWywjbLUJfECPSHhSiQ

hystrix.shareSecurityContext: true

logging:
  pattern.level: "%5p [${spring.application.name}, %X{traceId:-}, %X{spanId:-}]"
  level:
    root: INFO
    com.pharmeasy.proxy.*: INFO
spring:
  main:
    allow-bean-definition-overriding: true
  application.name: bookkeeper
  jackson.date-format: yyyy-MM-dd'T'HH:mm:ss.SSSZ
  cloud:
    stream:
      bindings:
        createBookkeeperInvoice:
          destination: createBookkeeperInvoice
          group: createBookkeeperInvoice
        createDebitNotes:
          destination: createDebitNotes
          group: createDebitNotes
        createCustomerDebitNotes:
          destination: return_complete_accounts
          group: book_keeper
        createCustomerInvoice:
          destination: retailer_invoice_creation
          group: book_keeper
        createCustomerDSInvoice:
          destination: retailer_store_invoice_creation
          group: book_keeper
        createConsolInvoice:
          destination: staging_invoice_creation
          group: book_keeper
        createCustomerDsDebitNote:
           destination: staging_darkstore_return_complete_accounts
           group: book_keeper
        updatePartnerEvent:
           destination: qa_partner_update_event
           group: book_keeper
        createAutoSettlement:
          destination: dev_cash_settlement
          group: book_keeper
        createRioInvoiceProducerSyncEvent:
          destination: rio_invoice_sync
          group: book_keeper
          producer:
            partitionKeyExpression: payload.vendorId
            partitionCount: 3
        createRioInvoiceConsumerSyncEvent:
          destination: rio_invoice_sync
          group: book_keeper
        sendInvoiceListProducerEvent:
          destination: settlement_invoices
          group: book_keeper
          producer:
            partitionCount: 3
        rioPayPaymentSync:
          destination: dev_rio_payment
          group: book_keeper
        rioPayDisbursementSync:
          destination: dev_rio_disbursement
          group: book_keeper
        createEInvoiceProducer:
          destination: einvoice_creation_bookkeeper
          group: book_keeper
          producer:
            partitionKeyExpression: payload.tenant
            partitionCount: 1
        createEInvoiceConsumer:
          destination: einvoice_creation_bookkeeper
          group: book_keeper
        createGenericEInvoiceProducer:
          destination: generic_einvoice_creation_bookkeeper
          group: book_keeper
          producer:
            partitionKeyExpression: payload.type
            partitionCount: 1
        genericEInvoiceRetryTriggerProducer:
          destination: generic_einvoice_auto_retry_trigger
          group: book_keeper
          producer:
            partitionCount: 2
        genericEInvoiceRetryTriggerConsumer:
          destination: generic_einvoice_auto_retry_trigger
          group: book_keeper
        genericEInvoiceCreationProducerV2:
          destination: generic_einvoice_creation
          group: book_keeper
          producer:
            partitionCount: 2
        genericEInvoiceCreationConsumerV2:
          destination: generic_einvoice_creation
          group: book_keeper
        retailerDebitNoteCreationProducer:
          destination: bk_debit_note_updates
          group: book_keeper
          producer:
            partitionKeyExpression: payload.type
            partitionCount: 1
        createGenericEInvoiceConsumer:
          destination: generic_einvoice_creation_bookkeeper
          group: book_keeper
        rioCreditNoteEvents:
          destination: rio_credit_note_pdf
          group: bookkeeper
        createGenericEWayBillProducer:
          destination: generic_ewaybill_creation_bookkeeper
          group: book_keeper
          producer:
            partitionKeyExpression: payload.sourceType
            partitionCount: 1
        createRioDebitNotesV1:
          destination: rio_return_complete_accounts
          group: book_keeper
        createGenericEWayBillConsumer:
          destination: generic_ewaybill_creation_bookkeeper
          group: book_keeper
        blockedVendorSettlementSinkProducer:
          destination: bookkeeper_blocked_vendor_settlement
          group: book_keeper
          producer:
            partitionCount: 5
        blockedVendorSettlementSinkConsumer:
          destination: bookkeeper_blocked_vendor_settlement
          group: book_keeper
        sendNotification:
          destination: notify
          producer:
            partitionCount: 3
        logisticsPayableAmountUpdateProducer:
          destination: dev_logistics_payable_amount_update
          group: spider-service
          producer:
            partitionKeyExpression: payload.rioOrderId
            partitionCount: 3
        createDcCallBackProducer:
          destination: pr_dc_callback
          group: book_keeper
          producer:
            partitionKeyExpression: payload.tenant
            partitionCount: 1
        createDeliveryChallan:
          destination: pr_dc_event
          group: book_keeper
        createBookkeeperRioInvoice:
          destination: rio_invoice_event
          group: bookkeeper
        createInvoiceSettlementProducer:
          destination: bookkeeper_rio_settlement_update
          group: bookkeeper
          producer:
            partitionKeyExpression: payload.retailerId
            partitionCount: 3
        createInvoiceSettlementConsumer:
          destination: bookkeeper_settlement_event_consumer
          group: bookkeeper
        createInvoiceSettlementNotifierProducer:
          destination: bookkeeper_settlement_event_consumer
          group: bookkeeper
          producer:
            partitionCount: 3
        rioCreditNotePdfConsumer:
          destination: vault_credit_note_pdf
          group: bookkeeper
        rioCreditNotePdfProducer:
          destination: vault_credit_note_pdf
          group: bookkeeper
          producer:
            partitionKeyExpression: payload.creditNoteNumber
            partitionCount: 3
        createMigrationProducer:
          destination: migration_data_vault
          group: book_keeper
          producer:
            partitionCount: 3
        createMigrationConsumer:
          destination: migration_data_vault
          group: book_keeper
        createRioDebitNotesV2:
          destination: rio_return_event
          group: book_keeper
        updateChequeStatus:
          destination: vault_cheque_file_upload
          group: book_keeper
        sendDraftFileProducer:
          destination: draft_invoice_file
          producer:
            partitionCount: 3
          group: book_keeper
        sendDraftFileConsumer:
          destination: draft_invoice_file
          group: book_keeper
        createExcelProducer:
          destination: create_excel_file
          producer:
            partitionCount: 3
        excelUrlConsumer:
          destination: excel_url_event
          group: book_keeper
        createSlipEntry:
          destination: rio_slip_event
          group: book_keeper
        sendFileConsumer:
          destination: send_email_event
          group: book_keeper
        sendFileProducer:
          destination: send_email_event
          group: book_keeper
        slipEventCallback:
            destination: rio_slip_update
            group: book_keeper
        makerCheckerDiffDownloadProducer:
          destination: maker_checker_diff_download
          group: book_keeper
          producer:
            partitionKeyExpression: payload.tenant
            partitionCount: 1
        makerCheckerDiffDownloadConsumer:
          destination: maker_checker_diff_download
          group: book_keeper
        makerCheckerApprovalProducer:
          destination: maker_checker_approval
          group: book_keeper
          producer:
            partitionKeyExpression: payload.entityType
            partitionCount: 1
        makerCheckerApprovalConsumer:
          destination: maker_checker_approval
          group: book_keeper
        paymentTransactionUpdates:
          destination: payment_transaction_updates
          group: book_keeper
          producer:
            partitionCount: 3
        bulkSettlementFileValidationProducer:
          destination: bulk_settlement_validation
          group: book_keeper
        bulkSettlementFileValidationConsumer:
          destination: bulk_settlement_validation
          group: book_keeper
        bulkSettlementFileProcessProducer:
          destination: bulk_settlement_process
          group: book_keeper
        bulkSettlementFileProcessConsumer:
          destination: bulk_settlement_process
          group: book_keeper
        cacheCornProducer:
          destination: cache_vendor_ledger
          group: bookkeeper
        cacheCornConsumer:
          destination: cache_vendor_ledger
          group: bookkeeper
      kafka:
        binder:
          brokers: localhost:9092
          headerMapperBeanName: binderHeaderMapper
          autoAddPartitions: true
          autoCreateTopics: true
          replicationFactor: 1
          minPartitionCount: 3
        bindings:
          genericEInvoiceRetryTriggerConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              configuration:
                max.poll.interval.ms: 1800000
                max.poll.records: 1
          genericEInvoiceCreationConsumerV2:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createCustomerDebitNotes:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createCustomerInvoice:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createDebitNotes:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createBookkeeperInvoice:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createConsolInvoice:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createCustomerDSInvoice:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createCustomerDsDebitNote:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createAutoSettlement:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          updatePartnerEvent:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createRioInvoiceConsumerSyncEvent:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createEInvoiceConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          rioPayPaymentSync:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createInvoiceSettlementProducer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createInvoiceSettlementConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          rioPayDisbursementSync:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          blockedVendorSettlementSinkProducer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          blockedVendorSettlementSinkConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createMigrationConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          rioCreditNotePdfConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createBookkeeperRioInvoice:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createRioDebitNotesV2:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createRioDebitNotesV1:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          updateChequeStatus:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createDeliveryChallan:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          sendDraftFileConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          excelUrlConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          createSlipEntry:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.records: 1
          makerCheckerDiffDownloadConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          slipEventCallback:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          sendFileConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 3600000
                max.poll.records: 1
          makerCheckerApprovalConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          bulkSettlementFileValidationConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          bulkSettlementFileProcessConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
          cacheCornConsumer:
            consumer:
              autoCommitOnError: true
              autoCommitOffset: true
              enableDlq: true
              dlqName: mco_retry_queue
              configuration:
                max.poll.interval.ms: 600000
                max.poll.records: 1
  datasource-write:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 1000
      idle-timeout: 300000
      maximum-pool-size: 20
      minimum-idle: 1
      pool-name: writer-pool
      jdbc-url: *****************************************************************************************************************
      username: root
      password: mercury
    test-on-borrow: true
    type: com.zaxxer.hikari.HikariDataSource
  datasource-read:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 1000
      idle-timeout: 300000
      maximum-pool-size: 20
      minimum-idle: 1
      pool-name: reader-pool
      username: root
      password: mercury
      jdbc-url: *****************************************************************************************************************
    test-on-borrow: true
    type: com.zaxxer.hikari.HikariDataSource
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
      naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5Dialect
        generate_statistics: false
        order_inserts: true
        order_updates: true
        jdbc:
          batch_size: 100
  redis:
    host: localhost
    port: 6379
    time-to-live: 600
  cache:
    type: redis
transaction-outbox:
  catalog:
  kafka-bootstrap-servers: localhost:9092
  relay.frequency.ms: 1000
server:
  shutdown: graceful

springdoc:
  swagger-ui:
    path: swagger-ui.html
    configUrl: /api/bookkeeper/v3/api-docs/swagger-config
    use-root-path: false
    url: /api/bookkeeper/v3/api-docs
