env: qa2
project_name: bookkeeper
namespace: qa2-thea
fqdn: "{{ env }}\\.(app|wms|thea|arsenal|vault|hustler|store)\\.scm\\.gomercury\\.in"
route: "/api/bookkeeper/"
tag: "{{ env }}-{{ build_number }}"
context: scm-staging-eks
profile: gomercury
account_no: ************
role_arn: arn:aws:iam::************:role/K8sS3ServiceAccount
min_replicas: 1
max_replicas: 1
requests_cpu: 130m
requests_memory: 230Mi
limits_cpu: 2.0
limits_memory: 2048Mi
sonar: "false"
config_map_yaml: |-
  application.yml: |-
    app:
      sqs:
        mrfifo: qa2_procurement_mr.fifo
      client.log: true
      reportEmailEnabled: false
      alertEmailEnabled: false
      url: http://qa2.vault.gomercury.in/advance-payment
      aws:
        s3:
          commissionBucket: go-vault-commission-analytics
          commissionPrefix: qa2/
          url: http://qa2.vault.scm.omercury.in/advance-payment
      retail-io:
        url: https://dev.retailio.in/api/oneroof
      partner:
        retailer : 3
        retailerInClinic : 13
        retailerFOFO : 12
        retailerCOCO : 17
        retailerHospitals : 15
        hospitals : 19
        retailerPL : 14
        retailerAlpha : 8
        cnf : 1
        distributor : 2
        holding : 4
        manufacturer : 5
      notificationservice:
        userId: pe_user
        event: CUSTOMER_LEDGER_REPORT_NEW1
        tenant: PE_PHARMA
        source: ABACUS_SERVICE
        url: https://notifications-requests.dev.pharmeasy.in
        fromMailId: <EMAIL>
        internalMailId: <EMAIL>
    spring:
      cloud:
        stream:
          bindings:
            createBookkeeperInvoice:
              destination: qa2.createBookkeeperInvoice
              group: book_keeper
            createDebitNotes:
              destination: qa2.createDebitNotes
              group: book_keeper
            createCustomerDebitNotes:
              destination: qa2.return_complete_accounts
              group: book_keeper
            createDcCallBackProducer:
              destination: qa2_pr_dc_callback
              group: book_keeper
            createDeliveryChallan:
              destination: qa2_pr_dc_event
              group: book_keeper
            createCustomerInvoice:
              destination: {{ env }}_retailer_invoice_creation
              group: book_keeper
            sendNotification:
              destination: {{ env }}_notify
            createGatePassEvent:
              destination: {{ env }}_gatepass_event
              group: book_keeper
            createCustomerDSInvoice:
              destination: {{ env }}_retailer_store_invoice_creation
              group: book_keeper
            createCustomerDsDebitNote:
              destination: qa2.darkstore_return_complete_accounts
              group: book_keeper
            createConsolInvoice:
              destination: {{ env }}_invoice_creation
              group: book_keeper
            createAutoSettlement:
              destination: {{ env }}_cash_settlement
              group: book_keeper
            updatePartnerEvent:
              destination: qa2_partner_update_event
              group: book_keeper
            commissionFranchisee:
              destination: {{ env }}_commission_analytics_s3_upload
            createRioInvoiceProducerSyncEvent:
              destination: qa2_rio_invoice_sync
              group: book_keeper
            createRioInvoiceConsumerSyncEvent:
              destination: qa2_rio_invoice_sync
              group: book_keeper
            createEInvoiceProducer:
              destination: qa2_einvoice_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.tenant
                partitionCount: 1
            createEInvoiceConsumer:
              destination: qa2_einvoice_creation_bookkeeper
              group: book_keeper
            createGenericEInvoiceProducer:
              destination: {{ env }}_generic_einvoice_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.type
                partitionCount: 1
            retailerDebitNoteCreationProducer:
              destination: {{ env }}_bk_debit_note_updates
              group: book_keeper
            createGenericEInvoiceConsumer:
              destination: {{ env }}_generic_einvoice_creation_bookkeeper
              group: book_keeper  
            sendInvoiceListProducerEvent:
              destination: qa2_settlement_invoices
            createVendorPortalConsumerSyncEvent:
              destination: {{ env }}_vp_bookkeeper_data_sync
              group: book_keeper
            createVendorPortalProducerSyncEvent:
              destination: {{ env }}_bookkeeper_vp_data_sync
              group: book_keeper
            rioPayPaymentSync:
              destination: {{ env }}_rio_payment
              group: book_keeper
            rioPayDisbursementSync:
              destination: {{ env }}_rio_disbursement_sync_to_ops
              group: book_keeper
            logisticsPayableAmountUpdateProducer:
              destination: {{ env }}_logistics_payable_amount_update
              group: bookkeeper
            createBookkeeperRioInvoice:
              destination: qa2_rio_invoice_event
              group: bookkeeper
            createRioDebitNotesV2:
              destination: qa2_rio_return_event
            createInvoiceSettlementProducer:
              destination: {{ env }}_bookkeeper_rio_settlement_update
              group: bookkeeper
            createInvoiceSettlementConsumer:
              destination: {{ env }}_bookkeeper_settlement_event_consumer
            createInvoiceSettlementNotifierProducer:
              destination: {{ env }}_bookkeeper_settlement_event_consumer
              group: bookkeeper
            rioCreditNoteEvents:
              destination: qa2_rio_credit_note_pdf
              group: bookkeeper
            createRioDebitNotesV1:
              destination: qa2_rio_return_complete_accounts
              group: book_keeper
            rioCreditNotePdfConsumer:
              destination: qa2_vault_credit_note_pdf
              group: bookkeeper
            rioCreditNotePdfProducer:
              destination: qa2_vault_credit_note_pdf
              group: bookkeeper
            blockedVendorSettlementSinkProducer:
              destination: {{ env }}_bookkeeper_blocked_vendor_settlement
              group: book_keeper
            blockedVendorSettlementSinkConsumer:
              destination: {{ env }}_bookkeeper_blocked_vendor_settlement
              group: book_keeper
            createMigrationProducer:
              destination: {{ env }}_migration_data_vault
              producer:
                partitionCount: 3
              group: book_keeper
            createMigrationConsumer:
              destination: {{ env }}_migration_data_vault
              group: book_keeper
            sendDraftFileProducer:
              destination: {{ env }}_draft_invoice_file
              producer:
                partitionCount: 3
              group: book_keeper
            sendDraftFileConsumer:
              destination: {{ env }}_draft_invoice_file
              group: book_keeper
            createSlipEntry:
              destination: {{ env }}_rio_slip_event
              group: book_keeper  
            slipEventCallback:
              destination: {{ env }}_rio_slip_update
              group: book_keeper
            sendFileConsumer:
              destination: {{ env }}_send_email_event
              group: book_keeper
            sendFileProducer:
              destination: {{ env }}_send_email_event
              group: book_keeper
            createGenericEWayBillProducer:
              destination: {{ env }}_generic_ewaybill_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.sourceType
                partitionCount: 1
            createGenericEWaybillConsumer:
              destination: {{ env }}_generic_ewaybill_creation_bookkeeper
              group: book_keeper
            makerCheckerDiffDownloadProducer:
              destination: {{ env }}_maker_checker_diff_download
              group: book_keeper
            makerCheckerDiffDownloadConsumer:
              destination: {{ env }}_maker_checker_diff_download
              group: book_keeper 
            makerCheckerApprovalProducer:
              destination: {{ env }}_maker_checker_approval
              group: book_keeper
            makerCheckerApprovalConsumer:
              destination: {{ env }}_maker_checker_approval
              group: book_keeper
            paymentTransactionUpdates:
              destination: {{ env }}_payment_transaction_updates
            genericEInvoiceRetryTriggerProducer:
              destination: {{ env }}_generic_einvoice_auto_retry_trigger
              group: book_keeper
              producer:
                partitionCount: 1
            genericEInvoiceRetryTriggerConsumer:
              destination: {{ env }}_generic_einvoice_auto_retry_trigger
              group: book_keeper
            genericEInvoiceCreationProducerV2:
              destination: {{ env }}_generic_einvoice_creation
              group: book_keeper
              producer:
                partitionCount: 1
            genericEInvoiceCreationConsumerV2:
              destination: {{ env }}_generic_einvoice_creation
              group: book_keeper
            bulkSettlementFileValidationProducer:
              destination: {{ env }}_bulk_settlement_validation
              group: book_keeper
            bulkSettlementFileValidationConsumer:
              destination: {{ env }}_bulk_settlement_validation
              group: book_keeper
            bulkSettlementFileProcessProducer:
              destination: {{ env }}_bulk_settlement_process
              group: book_keeper
            bulkSettlementFileProcessConsumer:
              destination: {{ env }}_bulk_settlement_process
              group: book_keeper
            cacheCornProducer:
              destination: {{ env }}_cache_vendor_ledger
              group: bookkeeper
            cacheCornConsumer:
              destination: {{ env }}_cache_vendor_ledger
              group: bookkeeper
          kafka:
              binder:
               brokers: kafka.scm.gomercury.in:9092
      datasource-write:
        hikari:
          password: ${WRITER_DB_PASS}
          jdbc-url: **********************************************************************************************************
          username: ${WRITER_DB_USER}
      datasource-read:
        hikari:
          password: ${READER_DB_PASS}
          jdbc-url: **********************************************************************************************************
          username: ${READER_DB_USER}
      profile: {{ env }}
      redis:
        host: redis-qa2.redis.svc.cluster.local
        port: 6379
        time-to-live: 86400000
    transaction-outbox:
      catalog:
      kafka-bootstrap-servers: kafka.scm.gomercury.in:9092
      relay.frequency.ms: 1000