env: production
project_name: bookkeeper
namespace: prod-thea
fqdn: "(app|wms|thea|arsenal|vault|hustler|store)\\.mercuryonline\\.co"
route: "/api/bookkeeper/"
tag: "{{ env }}-{{ build_number }}"
context: mercury-prod
account_no: ************
min_replicas: 4
max_replicas: 8
requests_cpu: 2
role_arn: arn:aws:iam::************:role/K8sMcoS3ServiceAccount
requests_memory: 3Gi
limits_cpu: 4
limits_memory: 4Gi
sonar: "false"
config_map_yaml: |-
  application.yml: |-
    app:
      aws:
        region: ap-south-1
        s3:
          bucket: scm-vault-data
          prefix: settlement
          sdcn:
            folderPrefix: sdcn/
            allPartnerFilePrefix: partners/generated/
            uploadedPartnerFilePrefix: partners/uploaded/
            draftCnPrefix: draftCn/
            deliveryChallanPrefix: deliveryChallan/
      client.log: false
      reportEmailEnabled: true
      alertEmailEnabled: true
      url: https://vault.mercuryonline.co/advance-payment
      partner:
        retailer : 3
        retailerInClinic : 13
        retailerFOFO : 12
        retailerCOCO : 17
        retailerHospitals : 15
        hospitals : 19
        retailerPL : 14
        retailerAlpha : 8
        cnf : 1
        distributor : 2
        holding : 4
        manufacturer : 5
      notificationservice:
        userId: pe_user
        event: CUSTOMER_LEDGER_REPORT_NEW1
        tenant: PE_PHARMA
        source: ABACUS_SERVICE
        url: notifications-requests.private.pharmeasy.in
        fromMailId: <EMAIL>
        internalMailId: <EMAIL>
      retail-io:
        url: https://order.retailio.in/api/oneroof
        version: 1.0.0
        source: vault-client
        key: ${RIO_VAULT_API_KEY}
      product:
        url: https://prod-catalog-api-gateway.internal.pharmeasy.in
      mercury:
        token: ${MERCURY_TOKEN}
    spring:
      profile: {{ env }}
      cloud:
        stream:
          bindings:
            createBookkeeperInvoice:
              destination: mco_create_book_keeper_invoice
              group: book_keeper
            createDebitNotes:
              destination: mco_create_debit_note
              group: book_keeper
            createDcCallBackProducer:
              destination: mco_pr_dc_callback
              group: book_keeper
            createDeliveryChallan:
              destination: mco_pr_dc_event
              group: book_keeper
            createCustomerDebitNotes:
              destination: mco_return_complete_accounts
              group: book_keeper
            createCustomerInvoice:
              destination: mco_retailer_invoice_creation
              group: book_keeper
            sendNotification:
              destination: mco_notify
            createGatePassEvent:
              destination: mco_gatepass_event
              group: book_keeper
            createCustomerDSInvoice:
              destination: mco_retailer_store_invoice_creation
              group: book_keeper
            createCustomerDsDebitNote:
              destination: mco_darkstore_return_complete_accounts
              group: book_keeper
            createConsolInvoice:
              group: book_keeper
              destination: mco_invoice_creation
            updatePartnerEvent:
              destination: mco_partner_update_event
              group: book_keeper
            createAutoSettlement:
              destination: mco_cash_settlement
              group: book_keeper
            createRioInvoiceProducerSyncEvent:
              destination: mco_rio_invoice_sync_v2
              group: book_keeper
            createRioInvoiceConsumerSyncEvent:
              destination: mco_rio_invoice_sync_v2
              group: book_keeper
            sendInvoiceListProducerEvent:
              destination: mco_settlement_invoices
              group: book_keeper
            createEInvoiceProducer:
              destination: mco_einvoice_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.tenant
                partitionCount: 1
            createEInvoiceConsumer:
              destination: mco_einvoice_creation_bookkeeper
              group: book_keeper
            rioPayPaymentSync:
              destination: mco_rio_payment
              group: book_keeper
            rioPayDisbursementSync:
              destination: mco_rio_disbursement_sync_to_ops
              group: book_keeper
            logisticsPayableAmountUpdateProducer:
              destination: mco_logistics_payable_amount_update
              group: bookkeeper
            createBookkeeperRioInvoice:
              destination: mco_rio_invoice_event
              group: bookkeeper
            createRioDebitNotesV2:
              destination: mco_rio_return_event
              group: book_keeper
            createInvoiceSettlementProducer:
              destination: mco_bookkeeper_rio_settlement_update
              group: bookkeeper
            rioCreditNoteEvents:
              destination: mco_rio_credit_note_pdf
              group: bookkeeper
            retailerDebitNoteCreationProducer:
              destination: mco_bk_debit_note_updates
              group: book_keeper
            createRioDebitNotesV1:
              destination: mco_rio_return_complete_accounts
              group: book_keeper
            rioCreditNotePdfConsumer:
              destination: mco_vault_credit_note_pdf
              group: bookkeeper
            rioCreditNotePdfProducer:
              destination: mco_vault_credit_note_pdf
              group: bookkeeper
            createInvoiceSettlementConsumer:
              destination: mco_bookkeeper_settlement_event_consumer
              group: bookkeeper
            createInvoiceSettlementNotifierProducer:
              destination: mco_bookkeeper_settlement_event_consumer
              group: bookkeeper
            blockedVendorSettlementSinkProducer:
              destination: mco_bookkeeper_blocked_vendor_settlement
              group: book_keeper
              producer:
                partitionCount: 5
            blockedVendorSettlementSinkConsumer:
              destination: mco_bookkeeper_blocked_vendor_settlement
              group: book_keeper 
            createMigrationProducer:
              destination: mco_migration_data_vault
              producer:
                partitionCount: 3
              group: book_keeper
            createMigrationConsumer:
              destination: mco_migration_data_vault
              group: book_keeper
            updateChequeStatus:
              destination: mco_vault_cheque_file_upload
              group: book_keeper
            sendDraftFileProducer:
              destination: mco_draft_invoice_file
              producer:
                partitionCount: 3
              group: book_keeper
            sendDraftFileConsumer:
              destination: mco_draft_invoice_file
              group: book_keeper
            createGenericEInvoiceProducer:
              destination: mco_generic_einvoice_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.type
                partitionCount: 1
            createGenericEInvoiceConsumer:
              destination: mco_generic_einvoice_creation_bookkeeper
              group: book_keeper  
            createSlipEntry:
              destination: mco_rio_slip_event
              group: book_keeper  
            slipEventCallback:
              destination: mco_rio_slip_update
              group: book_keeper
              producer:
                partitionCount: 2
            sendFileConsumer:
              destination: mco_send_email_event
              group: book_keeper
            sendFileProducer:
              destination: mco_send_email_event
              group: book_keeper
            createGenericEWayBillProducer:
              destination: mco_generic_ewaybill_creation_bookkeeper
              group: book_keeper
              producer:
                partitionKeyExpression: payload.sourceType
                partitionCount: 1 
            createGenericEWaybillConsumer:
              destination: mco_generic_ewaybill_creation_bookkeeper
              group: book_keeper
            createExcelProducer:
              destination: mco_create_excel_file
              producer:
                partitionCount: 3
            excelUrlConsumer:
              destination: mco_file_url_event
              group: book_keeper
            makerCheckerDiffDownloadProducer:
              destination: mco_maker_checker_diff_download
              group: book_keeper
              producer:
                partitionKeyExpression: payload.tenant
                partitionCount: 1
            makerCheckerDiffDownloadConsumer:
              destination: mco_maker_checker_diff_download
              group: book_keeper
            makerCheckerApprovalProducer:
              destination: mco_maker_checker_approval
              group: book_keeper
              producer:
                partitionKeyExpression: payload.entityType
                partitionCount: 1
            makerCheckerApprovalConsumer:
              destination: mco_maker_checker_approval
              group: book_keeper
            paymentTransactionUpdates:
              destination: mco_payment_transaction_updates
            genericEInvoiceRetryTriggerProducer:
              destination: mco_generic_einvoice_auto_retry_trigger
              group: book_keeper
              producer:
                partitionCount: 1
            genericEInvoiceRetryTriggerConsumer:
              destination: mco_generic_einvoice_auto_retry_trigger
              group: book_keeper
            genericEInvoiceCreationProducerV2:
              destination: mco_generic_einvoice_creation
              group: book_keeper
              producer:
                partitionCount: 3
            genericEInvoiceCreationConsumerV2:
              destination: mco_generic_einvoice_creation
              group: book_keeper
            bulkSettlementFileValidationProducer:
              destination: mco_bulk_settlement_validation
              group: book_keeper
            bulkSettlementFileValidationConsumer:
              destination: mco_bulk_settlement_validation
              group: book_keeper
            bulkSettlementFileProcessProducer:
              destination: mco_bulk_settlement_process
              group: book_keeper
            bulkSettlementFileProcessConsumer:
              destination: mco_bulk_settlement_process
              group: book_keeper
            cacheCornProducer:
              destination: mco_cache_vendor_ledger
              group: bookkeeper
            cacheCornConsumer:
              destination: mco_cache_vendor_ledger
              group: bookkeeper
          kafka:
             binder:
                brokers: kafka01.neo.mercuryonline.co:9092,kafka02.neo.mercuryonline.co:9092,kafka03.neo.mercuryonline.co:9092
                replicationFactor: 2
      datasource-write:
        hikari:
          password: ${WRITER_DB_PASS}
          jdbc-url: *************************************************************************************************************************************
          username: ${WRITER_DB_USER}
      datasource-read:
        hikari:
          password: ${READER_DB_PASS}
          jdbc-url: *********************************************************************************************************************************************
          username: ${READER_DB_USER}
      jpa:
        show-sql: false
      redis:
        host: master.prod-mercury.vawqno.aps1.cache.amazonaws.com
        port: 6379
        timeout: 60000
        ssl: true
        password: ${REDIS_PASS}
        database: 1
    transaction-outbox:
      catalog:
      kafka-bootstrap-servers: kafka01.neo.mercuryonline.co:9092,kafka02.neo.mercuryonline.co:9092,kafka03.neo.mercuryonline.co:9092
      relay.frequency.ms: 1000
